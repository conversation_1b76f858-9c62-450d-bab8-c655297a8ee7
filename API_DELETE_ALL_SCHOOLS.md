# Delete All Schools API Endpoint

## Overview
New API endpoint to delete all schools in the database with a single request.

## Endpoint Details

### Route
```
DELETE /school/delete-all-schools
```

### Authentication & Authorization
- **Authentication**: Required (JWT token)
- **Authorization**: Only `super` users can access this endpoint
- **Middleware**: `authenticate`, `authorize(['super'])`

### Request
- **Method**: DELETE
- **Headers**: 
  - `Content-Type: application/json`
  - `Authorization: Bearer <token>`
- **Body**: No body required

### Response

#### Success Response (200)
```json
{
  "message": "All 15 school records deleted successfully",
  "deletedCount": 15
}
```

#### No Schools Found (404)
```json
{
  "message": "No schools found to delete"
}
```

#### Error Response (500)
```json
{
  "message": "Database error message"
}
```

## Implementation Details

### Controller Function: `deleteAllSchools`
```javascript
const deleteAllSchools = async (req, res) => {
  try {
    // First, count how many schools exist
    const schoolCount = await School.countDocuments();
    
    if (schoolCount === 0) {
      return res.status(404).json({ message: 'No schools found to delete' });
    }
    
    // Delete all school records
    const result = await School.deleteMany({});
    
    res.json({ 
      message: `All ${result.deletedCount} school records deleted successfully`,
      deletedCount: result.deletedCount 
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
```

### Database Operation
- Uses MongoDB `deleteMany({})` to remove all documents
- Counts documents first to provide meaningful response
- Returns the actual number of deleted records

## Security Considerations

1. **Super User Only**: Only users with 'super' role can access
2. **Authentication Required**: Valid JWT token mandatory
3. **Destructive Operation**: Deletes ALL schools - use with extreme caution
4. **No Undo**: Operation is irreversible

## Frontend Integration

### Service Function
```typescript
export async function deleteAllSchools() {
    const response = await fetch(`${BASE_API_URL}/school/delete-all-schools`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error deleting all schools:", response.statusText);
        throw new Error("Failed to delete all schools");
    }
    const data = await response.json();
    return data;
}
```

### Usage in Components
```typescript
const handleDeleteAll = async () => {
  if (confirm(`Are you sure you want to delete ALL schools? This action cannot be undone.`)) {
    try {
      const result = await deleteAllSchools();
      console.log(result.message); // "All X school records deleted successfully"
      // Refresh data, show notification, etc.
    } catch (error) {
      console.error("Error:", error);
    }
  }
};
```

## Benefits Over Previous Approach

1. **Performance**: Single database operation vs multiple queries
2. **Atomicity**: All-or-nothing operation
3. **Efficiency**: No need to fetch all IDs first
4. **Cleaner Code**: Dedicated endpoint for specific operation
5. **Better Error Handling**: Specific responses for different scenarios

## Testing

### Manual Testing
```bash
# Using curl
curl -X DELETE \
  http://localhost:5000/api/school/delete-all-schools \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### Expected Behaviors
- ✅ Deletes all schools when schools exist
- ✅ Returns 404 when no schools exist
- ✅ Requires authentication
- ✅ Requires super user role
- ✅ Returns count of deleted records
