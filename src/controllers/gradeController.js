const Grade = require('../models/Grade'); // Assuming you have a Grade model
const { ensureUniqueId } = require('../utils/generateId');
const mongoose = require('mongoose');

const testGradeResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is grade' });
};

// // Get all grade records
const getAllGrades = async (req, res) => {
  try {
    const grades = await Grade.find();
    res.json(grades);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Create a new grade record
const createGrade = async (req, res) => {
  try {
    // If user is a teacher, verify they can create grades for this class/subject
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to create grades', teacher: req.teacher, user: req.user });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(req.body.class_id)) {
        return res.status(403).json({ message: 'You are not authorized to create grades for this class' });
      }

      // Check if teacher is assigned to the subject (subjects are stored as strings)
      if (!req.teacher.assigned_subjects.includes(req.body.subject)) {
        return res.status(403).json({ message: 'You are not authorized to create grades for this subject' });
      }
    }

    // Enhanced grade creation with term integration
    const gradeData = { ...req.body };

    // If term_id is provided, validate it and auto-populate related fields
    if (gradeData.term_id) {
      const Term = require('../models/Term');
      const term = await Term.findById(gradeData.term_id);

      if (!term) {
        return res.status(400).json({ message: 'Invalid term ID provided' });
      }

      // Validate that the term belongs to the same school
      if (term.school_id.toString() !== gradeData.school_id) {
        return res.status(400).json({ message: 'Term does not belong to the specified school' });
      }

      // Validate sequence_number against term's sequences
      if (gradeData.sequence_number) {
        const validSequence = term.sequences.find(seq => seq.sequence_number === gradeData.sequence_number);
        if (!validSequence) {
          return res.status(400).json({
            message: `Invalid sequence number. Valid sequences for this term: ${term.sequences.map(s => s.sequence_number).join(', ')}`
          });
        }
      } else {
        // Default to first sequence if not provided
        gradeData.sequence_number = term.sequences[0]?.sequence_number || 1;
      }

      // Auto-populate legacy fields for backward compatibility
      gradeData.term = term.name;
      gradeData.academic_year = term.academic_year;
    } else if (gradeData.term && gradeData.academic_year) {
      // Legacy mode: try to find matching term
      const Term = require('../models/Term');
      const termMapping = {
        'First Term': 1,
        'Second Term': 2,
        'Third Term': 3,
        'Premier Trimestre': 1,
        'Deuxième Trimestre': 2,
        'Troisième Trimestre': 3
      };

      const termNumber = termMapping[gradeData.term];
      if (termNumber) {
        const term = await Term.findOne({
          school_id: gradeData.school_id,
          term_number: termNumber,
          academic_year: gradeData.academic_year
        });

        if (term) {
          gradeData.term_id = term._id;
          gradeData.sequence_number = gradeData.sequence_number || term.sequences[0]?.sequence_number || 1;
        }
      }
    } else {
      // No term information provided, try to use current term
      const Term = require('../models/Term');
      const currentTerm = await Term.findOne({
        school_id: gradeData.school_id,
        is_current: true
      });

      if (currentTerm) {
        gradeData.term_id = currentTerm._id;
        gradeData.term = currentTerm.name;
        gradeData.academic_year = currentTerm.academic_year;
        gradeData.sequence_number = gradeData.sequence_number || currentTerm.sequences[0]?.sequence_number || 1;
      } else {
        return res.status(400).json({ message: 'No current term found. Please specify term information.' });
      }
    }

    const newGrade = new Grade(gradeData);
    await newGrade.save();

    // Populate the response with term information
    const populatedGrade = await Grade.findById(newGrade._id)
      .populate('term_id', 'name term_number academic_year sequences')
      .populate('subject_id', 'name')
      .populate('student_id', 'first_name last_name')
      .populate('exam_type', 'type');

    res.status(201).json({
      message: 'Grade created successfully',
      grade: populatedGrade
    });
  } catch (err) {
    if (err.code === 11000) {
      // Duplicate key error
      res.status(400).json({
        message: 'A grade already exists for this student, subject, exam type, term, and sequence combination'
      });
    } else {
      res.status(400).json({ message: err.message });
    }
  }
};

// // Get a grade record by ID
const getGradeById = async (req, res) => {
  const _id = new mongoose.Types.ObjectId(req.params.id);
  try {
    const grade = await Grade.findById(_id);
    if (!grade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }
    res.json(grade);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Update grade record by ID
const updateGradeById = async (req, res) => {
  try {
    // First, get the existing grade to check permissions
    const existingGrade = await Grade.findById(req.params.id);
    if (!existingGrade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }

    // If user is a teacher, verify they can update this grade
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to update grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(existingGrade.class_id.toString())) {
        return res.status(403).json({ message: 'You are not authorized to update grades for this class' });
      }

      // Note: Subject verification would need the actual subject name from the grade record
      // This might require populating the subject or storing subject name in the grade
    }

    const updatedGrade = await Grade.findByIdAndUpdate(req.params.id, req.body, { new: true });
    res.json({ message: 'Grade updated successfully', grade: updatedGrade });
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Delete grade record by ID
const deleteGradeById = async (req, res) => {
  try {
    // First, get the existing grade to check permissions
    const existingGrade = await Grade.findById(req.params.id);
    if (!existingGrade) {
      return res.status(404).json({ message: 'Grade record not found' });
    }

    // If user is a teacher, verify they can delete this grade
    if (req.user.role === 'teacher') {
      const { hasPermission } = require('../middleware/teacherMiddleware');

      // Check if teacher has permission to enter/edit grades (includes delete)
      if (!hasPermission(req, 'academic_records', 'enter_edit_grades_assigned_classes')) {
        return res.status(403).json({ message: 'You do not have permission to delete grades' });
      }

      // Check if teacher is assigned to the class
      if (!req.teacher || !req.teacher.assigned_class_ids.includes(existingGrade.class_id.toString())) {
        return res.status(403).json({ message: 'You are not authorized to delete grades for this class' });
      }

      // Note: Subject verification would need the actual subject name from the grade record
    }

    await Grade.findByIdAndDelete(req.params.id);
    res.json({ message: 'Grade record deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};


// Delete multiple grade records by IDs
const deleteMultipleGrades = async (req, res) => {
  const { ids } = req.body; // Expecting an array of grade IDs in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete grade records where _id is in the provided array of IDs
    const result = await Grade.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No grade records found for the provided IDs' });
    }

    res.json({ message: `${result.deletedCount} grade records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
const getGradeRecords = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      term_id,
      sequence_number,
      exam_type_id,
      academic_year,
      student_id,
      page = 1,
      limit = 50
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object for basic filters
    const filter = { school_id: schoolObjectId };

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (subject_id) filter.subject_id = new mongoose.Types.ObjectId(subject_id);
    if (exam_type_id) filter.exam_type = new mongoose.Types.ObjectId(exam_type_id);
    if (student_id) filter.student_id = new mongoose.Types.ObjectId(student_id);
    if (sequence_number) filter.sequence_number = parseInt(sequence_number);

    // Enhanced term filtering - support both new term_id and legacy term string
    if (term_id) {
      filter.term_id = new mongoose.Types.ObjectId(term_id);
    } else if (term) {
      // Legacy support: filter by term string
      filter.term = term;
    }

    // Academic year filtering - can be used with or without term_id
    if (academic_year && !term_id) {
      // If no term_id specified but academic_year is, filter by academic_year
      filter.academic_year = academic_year;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build aggregation pipeline for class filtering
    let pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' },
      {
        $lookup: {
          from: 'classes',
          localField: 'student.class_id',
          foreignField: '_id',
          as: 'class'
        }
      },
      { $unwind: { path: '$class', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'subjects',
          localField: 'subject_id',
          foreignField: '_id',
          as: 'subject'
        }
      },
      { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'examtypes',
          localField: 'exam_type',
          foreignField: '_id',
          as: 'examType'
        }
      },
      { $unwind: { path: '$examType', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'terms',
          localField: 'term_id',
          foreignField: '_id',
          as: 'termInfo'
        }
      },
      { $unwind: { path: '$termInfo', preserveNullAndEmptyArrays: true } }
    ];

    // Add class filter if provided
    if (class_id) {
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add teacher class filter if user is a teacher
    if (req.user.role === 'teacher' && req.teacherClassIds && req.teacherClassIds.length > 0) {
      pipeline.push({
        $match: {
          'student.class_id': {
            $in: req.teacherClassIds.map(id => new mongoose.Types.ObjectId(id))
          }
        }
      });
    }

    // Add sorting and pagination
    pipeline.push(
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: parseInt(limit) }
    );

    // Get grade records with aggregation
    const gradeRecords = await Grade.aggregate(pipeline);

    // Get total count for pagination (separate aggregation without pagination)
    let countPipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' }
    ];

    if (class_id) {
      countPipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add teacher class filter for count pipeline too
    if (req.user.role === 'teacher' && req.teacherClassIds && req.teacherClassIds.length > 0) {
      countPipeline.push({
        $match: {
          'student.class_id': {
            $in: req.teacherClassIds.map(id => new mongoose.Types.ObjectId(id))
          }
        }
      });
    }

    countPipeline.push({ $count: 'total' });
    const countResult = await Grade.aggregate(countPipeline);
    const totalRecords = countResult.length > 0 ? countResult[0].total : 0;

    // Format the response with enhanced term information
    const formattedRecords = gradeRecords.map(record => {
      // Get sequence information from term
      let sequenceName = '';
      if (record.termInfo && record.sequence_number) {
        const sequence = record.termInfo.sequences.find(seq => seq.sequence_number === record.sequence_number);
        sequenceName = sequence ? sequence.sequence_name : `Séquence ${record.sequence_number}`;
      }

      return {
        _id: record._id,
        student_name: record.student ?
          `${record.student.first_name} ${record.student.last_name}` : 'Unknown Student',
        student_id: record.student?.student_id || 'N/A',
        class_name: record.class?.name || 'Unknown Class',
        subject_name: record.subject?.name || 'Unknown Subject',
        exam_type: record.examType?.type || 'Unknown Exam Type',

        // Enhanced term information
        term: record.termInfo?.name || record.term || 'Unknown Term',
        term_id: record.term_id,
        term_number: record.termInfo?.term_number,
        sequence_number: record.sequence_number,
        sequence_name: sequenceName,
        academic_year: record.termInfo?.academic_year || record.academic_year || 'Unknown Year',

        // Grade information
        score: record.score,
        grade: record.grade,
        comments: record.comments,
        date_entered: record.createdAt,

        // Additional term metadata
        term_start_date: record.termInfo?.start_date,
        term_end_date: record.termInfo?.end_date,
        is_current_term: record.termInfo?.is_current || false
      };
    });

    console.log('Grade records found:', formattedRecords.length);

    res.status(200).json({
      grade_records: formattedRecords,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(totalRecords / parseInt(limit)),
        total_records: totalRecords,
        per_page: parseInt(limit)
      },
      message: 'Grade records retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching grade records:', error);
    res.status(500).json({ message: 'Internal server error', error: error });
  }
};

// Get grade statistics for a school
const getGradeStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      term,
      term_id,
      sequence_number,
      academic_year,
      class_id,
      subject_id
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build aggregation pipeline
    let pipeline = [
      {
        $match: {
          school_id: schoolObjectId
        }
      }
    ];

    // Add lookup for student to get class information if class_id filter is needed OR if user is a teacher
    if (class_id || (req.user.role === 'teacher' && req.teacherClassIds && req.teacherClassIds.length > 0)) {
      pipeline.push({
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      });
      pipeline.push({ $unwind: '$student' });

      // Add class filter if provided
      if (class_id) {
        pipeline.push({
          $match: {
            'student.class_id': new mongoose.Types.ObjectId(class_id)
          }
        });
      }

      // Add teacher class filter
      if (req.user.role === 'teacher' && req.teacherClassIds && req.teacherClassIds.length > 0) {
        pipeline.push({
          $match: {
            'student.class_id': {
              $in: req.teacherClassIds.map(id => new mongoose.Types.ObjectId(id))
            }
          }
        });
      }
    }

    // Enhanced term filtering - support both new term_id and legacy term string
    const additionalFilters = {};
    if (term_id) {
      additionalFilters.term_id = new mongoose.Types.ObjectId(term_id);
    } else if (term) {
      // Legacy support: filter by term string
      additionalFilters.term = term;
    }

    if (sequence_number) {
      additionalFilters.sequence_number = parseInt(sequence_number);
    }

    if (academic_year && !term_id) {
      // If no term_id specified but academic_year is, filter by academic_year
      additionalFilters.academic_year = academic_year;
    }

    if (subject_id) {
      additionalFilters.subject_id = new mongoose.Types.ObjectId(subject_id);
    }

    if (Object.keys(additionalFilters).length > 0) {
      pipeline.push({
        $match: additionalFilters
      });
    }

    // Teacher filtering is now handled above in the pipeline

    // Add grouping stage for statistics
    pipeline.push({
      $group: {
        _id: null,
        totalGrades: { $sum: 1 },
        averageScore: { $avg: '$score' },
        highestScore: { $max: '$score' },
        lowestScore: { $min: '$score' },
        passCount: {
          $sum: {
            $cond: [{ $gte: ['$score', 10] }, 1, 0]
          }
        }
      }
    });

    console.log('Grade stats aggregation pipeline:', JSON.stringify(pipeline, null, 2));

    // Get grade statistics
    const gradeStats = await Grade.aggregate(pipeline);

    const stats = gradeStats.length > 0 ? gradeStats[0] : {
      totalGrades: 0,
      averageScore: 0,
      highestScore: 0,
      lowestScore: 0,
      passCount: 0
    };

    // Calculate pass rate
    const passRate = stats.totalGrades > 0 ?
      Math.round((stats.passCount / stats.totalGrades) * 100) : 0;

    // Round average score
    stats.averageScore = Math.round(stats.averageScore || 0);

    console.log('Grade stats result:', stats);

    res.status(200).json({
      stats: {
        totalGrades: stats.totalGrades,
        averageScore: stats.averageScore,
        highestScore: stats.highestScore || 0,
        lowestScore: stats.lowestScore || 0,
        passRate: passRate
      },
      message: 'Grade statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching grade stats:', error);
    res.status(500).json({ message: 'Internal server error', error: error });
  }
};

// Export grades to PDF
const exportGradesPDF = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      term_id,
      sequence_number,
      exam_type_id,
      academic_year,
      student_id
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    const filter = { school_id: schoolObjectId };
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (subject_id) filter.subject_id = new mongoose.Types.ObjectId(subject_id);
    if (student_id) filter.student_id = new mongoose.Types.ObjectId(student_id);
    if (sequence_number) filter.sequence_number = parseInt(sequence_number);
    if (term_id) {
      filter.term_id = new mongoose.Types.ObjectId(term_id);
    } else if (term) {
      filter.term = term;
    }
    if (academic_year && !term_id) {
      filter.academic_year = academic_year;
    }
    if (exam_type_id) {
      filter.exam_type = new mongoose.Types.ObjectId(exam_type_id);
    }

    const School = require('../models/School');
    const Grade = require('../models/Grade');
    const schoolDoc = await School.findById(school_id);
    const schoolName = schoolDoc?.name || 'School Name';

    let pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' },
      {
        $lookup: {
          from: 'classes',
          localField: 'student.class_id',
          foreignField: '_id',
          as: 'class'
        }
      },
      { $unwind: { path: '$class', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'subjects',
          localField: 'subject_id',
          foreignField: '_id',
          as: 'subject'
        }
      },
      { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'examtypes',
          localField: 'exam_type',
          foreignField: '_id',
          as: 'examType'
        }
      },
      { $unwind: { path: '$examType', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'terms',
          localField: 'term_id',
          foreignField: '_id',
          as: 'termInfo'
        }
      },
      { $unwind: { path: '$termInfo', preserveNullAndEmptyArrays: true } }
    ];

    if (class_id) {
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    pipeline.push({ $sort: { createdAt: -1 } });

    const gradeRecords = await Grade.aggregate(pipeline);

    const formattedRecords = gradeRecords.map(record => ({
      student_name: record.student
        ? `${record.student.first_name} ${record.student.middle_name} ${record.student.last_name}`
        : 'Unknown Student',
      student_id: record.student?.student_id || 'N/A',
      class_name: record.class?.name || 'Unknown Class',
      subject_name: record.subject?.name || 'Unknown Subject',
      exam_type: record.examType?.type || 'Unknown Exam Type',
      term: record.termInfo?.name || record.term || 'Unknown Term',
      academic_year: record.academic_year || 'Unknown Year',
      score: (record.score).toFixed(1),
      grade: record.grade,
      sequence_number: record.sequence_number || 'N/A'
    }));

    const PDFDocument = require('pdfkit');
    const doc = new PDFDocument({ margin: 50 });

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename=grades-export.pdf');
    doc.pipe(res);

    // HEADER SECTION
    const firstRecord = gradeRecords[0] || {};
    const className = firstRecord?.class?.name || 'Class';
    const subjectName = firstRecord?.subject?.name || 'Subject';
    const termName = firstRecord?.termInfo?.name || firstRecord?.term || 'Term';
    const sequence = firstRecord?.sequence_number || 'Sequence';
    const year = firstRecord?.academic_year || 'Academic Year';

    doc.fontSize(18).text(`${schoolName}`, { align: 'center' });
    doc.moveDown(0.3);
    doc.fontSize(14).text(`Class: ${className}    |    Subject: ${subjectName}`, { align: 'center' });
    doc.moveDown(0.2);
    doc.fontSize(12).text(`Term: ${termName} - Sequence ${sequence}    |    Academic Year: ${year}`, { align: 'center' });
    doc.moveDown(1);
    doc.moveTo(50, doc.y).lineTo(550, doc.y).stroke();

    // Helper function to draw table header and return new y position
    function drawTableHeader(doc, y) {
      doc.fontSize(11).font('Helvetica-Bold');
      doc.text('Student Name', 50, y, { width: 200 });
      doc.text('Student ID', 200, y, { width: 100 });
      doc.text('Score (/20)', 340, y, { width: 60 });
      doc.text('Grade', 430, y, { width: 100 });
      y += 15;
      doc.moveTo(50, y).lineTo(550, y).stroke();
      return y + 5;
    }

    // Initial table header
    let yPosition = doc.y + 10;
    yPosition = drawTableHeader(doc, yPosition);

    // TABLE ROWS with pagination and header redraw
    doc.font('Helvetica').fontSize(10);
    for (const record of formattedRecords) {
      if (yPosition > 750) {
        doc.addPage();
        yPosition = 50;
        yPosition = drawTableHeader(doc, yPosition);
      }

      doc.text(record.student_name, 50, yPosition, { width: 200 });
      doc.text(record.student_id, 200, yPosition, { width: 100 });
      doc.text(`${record.score}`, 340, yPosition, { width: 60 });
      doc.text(record.grade, 430, yPosition, { width: 100 });

      yPosition += 15;
      doc.moveTo(50, yPosition).lineTo(550, yPosition).strokeColor('#eee').stroke();
    }

    // FOOTER: generation date and page number on each page
    const totalPages = doc.bufferedPageRange().count;

    for (let i = 0; i < totalPages; i++) {
      doc.switchToPage(i);
      doc.fontSize(9).fillColor('gray')
        .text(`Generated on: ${new Date().toLocaleString()}`, 50, 760, { align: 'left' });
      doc.fontSize(9).fillColor('gray')
        .text(`Page ${i + 1} of ${totalPages}`, 500, 760, { align: 'right' });
    }

    doc.end();
  } catch (error) {
    console.error('Error exporting grades to PDF:', error);
    res.status(500).json({ message: 'Internal server error', error });
  }
};

// Export grades to Excel
const exportGradesExcel = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      term,
      term_id,
      sequence_number,
      exam_type_id,
      academic_year,
      student_id
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object for basic filters
    const filter = { school_id: schoolObjectId };

    // Apply teacher-specific filters using middleware helper
    const { applyTeacherFilters } = require('../middleware/teacherMiddleware');
    applyTeacherFilters(filter, req);

    if (subject_id) filter.subject_id = new mongoose.Types.ObjectId(subject_id);
    if (student_id) filter.student_id = new mongoose.Types.ObjectId(student_id);
    if (sequence_number) filter.sequence_number = parseInt(sequence_number);

    // Enhanced term filtering - support both new term_id and legacy term string
    if (term_id) {
      filter.term_id = new mongoose.Types.ObjectId(term_id);
    } else if (term) {
      // Legacy support: filter by term string
      filter.term = term;
    }

    // Academic year filtering - can be used with or without term_id
    if (academic_year && !term_id) {
      // If no term_id specified but academic_year is, filter by academic_year
      filter.academic_year = academic_year;
    }

    // Exam type filtering (now optional)
    if (exam_type_id) {
      filter.exam_type = new mongoose.Types.ObjectId(exam_type_id);
    }

    // Build aggregation pipeline for class filtering
    let pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: 'students',
          localField: 'student_id',
          foreignField: '_id',
          as: 'student'
        }
      },
      { $unwind: '$student' },
      {
        $lookup: {
          from: 'classes',
          localField: 'student.class_id',
          foreignField: '_id',
          as: 'class'
        }
      },
      { $unwind: { path: '$class', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'subjects',
          localField: 'subject_id',
          foreignField: '_id',
          as: 'subject'
        }
      },
      { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'examtypes',
          localField: 'exam_type',
          foreignField: '_id',
          as: 'examType'
        }
      },
      { $unwind: { path: '$examType', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'terms',
          localField: 'term_id',
          foreignField: '_id',
          as: 'termInfo'
        }
      },
      { $unwind: { path: '$termInfo', preserveNullAndEmptyArrays: true } }
    ];

    // Add class filter if provided
    if (class_id) {
      pipeline.push({
        $match: {
          'student.class_id': new mongoose.Types.ObjectId(class_id)
        }
      });
    }

    // Add sorting
    pipeline.push({ $sort: { createdAt: -1 } });

    // Get grade records with aggregation
    const gradeRecords = await Grade.aggregate(pipeline);

    // Format the response
    const formattedRecords = gradeRecords.map(record => ({
      'Student Name': record.student ?
        `${record.student.first_name} ${record.student.last_name}` : 'Unknown Student',
      'Student ID': record.student?.student_id || 'N/A',
      'Class': record.class?.name || 'Unknown Class',
      'Subject': record.subject?.name || 'Unknown Subject',
      'Exam Type': record.examType?.type || 'Unknown Exam Type',
      'Term': record.term,
      'Academic Year': record.academic_year,
      'Score (/20)': (record.score).toFixed(1),
      'Grade': record.grade,
      'Comments': record.comments || '',
      'Date Entered': record.createdAt ? new Date(record.createdAt).toLocaleDateString() : ''
    }));

    // Create Excel file using xlsx
    const XLSX = require('xlsx');
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(formattedRecords);

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Grades');

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=grades-export.xlsx');

    res.send(buffer);
  } catch (error) {
    console.error('Error exporting grades to Excel:', error);
    res.status(500).json({ message: 'Internal server error', error: error });
  }
};

// Get available terms and sequences for grade creation
const getAvailableTerms = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const Term = require('../models/Term');

    // Get all active terms for the school
    const terms = await Term.find({
      school_id: school_id,
      is_active: true
    }).sort({ term_number: 1 });

    // Format terms with sequences for easy frontend consumption
    const formattedTerms = terms.map(term => ({
      _id: term._id,
      name: term.name,
      term_number: term.term_number,
      academic_year: term.academic_year,
      is_current: term.is_current,
      sequences: term.sequences.map(seq => ({
        sequence_number: seq.sequence_number,
        sequence_name: seq.sequence_name
      })),
      start_date: term.start_date,
      end_date: term.end_date
    }));

    // Also return the current term separately for convenience
    const currentTerm = terms.find(term => term.is_current);

    res.status(200).json({
      terms: formattedTerms,
      current_term: currentTerm ? {
        _id: currentTerm._id,
        name: currentTerm.name,
        term_number: currentTerm.term_number,
        academic_year: currentTerm.academic_year,
        sequences: currentTerm.sequences
      } : null,
      message: 'Available terms retrieved successfully'
    });

  } catch (error) {
    console.error('Error fetching available terms:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  testGradeResponse,
  getAllGrades,
  createGrade,
  getGradeById,
  updateGradeById,
  deleteGradeById,
  deleteMultipleGrades,
  getGradeRecords,
  getGradeStats,
  exportGradesPDF,
  exportGradesExcel,
  getAvailableTerms
};
