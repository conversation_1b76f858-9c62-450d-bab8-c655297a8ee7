// AcademicYearContext.tsx
"use client";
import React, { createContext, useContext, ReactNode } from 'react';
import useAcademicYear from '@/components/utils/useAcademicYear';
// Define the shape of the context
interface AcademicYearContextType {
  currentAcademicYear: string;
  yearLoading: boolean;
  refreshAcademicYear: () => void;
}

// Create the context with a default value
const AcademicYearContext = createContext<AcademicYearContextType | undefined>(undefined);

// Provider component
export const AcademicYearProvider = ({ children }: { children: ReactNode }) => {
  const { currentAcademicYear, yearLoading, refreshAcademicYear } = useAcademicYear();

  return (
    <AcademicYearContext.Provider
      value={{ currentAcademicYear, yearLoading, refreshAcademicYear }}
    >
      {children}
    </AcademicYearContext.Provider>
  );
};

// Custom hook to use the context
export const useAcademicYearContext = () => {
  const context = useContext(AcademicYearContext);
  if (!context) {
    throw new Error('useAcademicYearContext must be used within an AcademicYearProvider');
  }
  return context;
};
