import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';

export default function BackRounded() {
  const router = useRouter();

  return (
    <button
      onClick={() => router.back()}
      className="flex text-foreground items-center space-x-2 px-4 py-2 rounded-full border border-gray-300 hover:bg-gray-100 transition"
      type="button"
    >
      <ArrowLeft className="w-5 h-5" />
      <span className="text-sm font-medium">Back</span>
    </button>
  );
}
