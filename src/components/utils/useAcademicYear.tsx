import { useState, useEffect } from 'react';
import { getAcademicYears } from '@/app/services/AcademicYearServices';
import { AcademicYearSchema } from '@/app/models/AcademicYear';

const useAcademicYear = () => {
  const [currentAcademicYear, setCurrentAcademicYear] = useState<string>('');
  const [yearLoading, setYearLoading] = useState(false);

  // Ensure localStorage is only used on the client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedYear = localStorage.getItem('currentAcademicYear');
      if (storedYear) {
        setCurrentAcademicYear(storedYear);
      }
    }
  }, []);

  const getCurrentAcademicYear = (academicYears: AcademicYearSchema[]): string => {
    const normalize = (dateStr: string | Date) => {
      const d = new Date(dateStr);
      d.setHours(0, 0, 0, 0);
      return d;
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Sort the academic years by the most recent first
    const sortedAcademicYears = academicYears.sort((a, b) => new Date(b.start_date).getTime() - new Date(a.start_date).getTime());

    // Grace period: July (6) and August (7)
    const isWithinGracePeriod = today.getMonth() === 6 || today.getMonth() === 7; // July or August (months are 0-indexed)

    // If in the grace period, pick the next academic year (even if it's not started yet)
    const currentAcademicYear = academicYears.find((year) => {
      const start = normalize(year.start_date);
      const end = normalize(year.end_date);

      if (isWithinGracePeriod) {
        // During grace period, consider the upcoming year as valid if it's not yet started
        return today <= end;
      }

      return today >= start && today <= end;
    });

    if (currentAcademicYear) {
      return currentAcademicYear.academic_year;
    }

    // Fallback to the previous academic year if no current academic year is found
    if (sortedAcademicYears.length > 0) {
      return sortedAcademicYears[0].academic_year; // Fallback to the most recent academic year
    }

    return '';
  };

  const fetchAcademicYear = async () => {
    try {
      setYearLoading(true);

      const years = await getAcademicYears();
      const current = getCurrentAcademicYear(years);
      console.log('📅 Fetched academic years:', years); // Add this

      if (current) {
        // Only update if the current academic year has changed
        if (current !== currentAcademicYear) {
          console.log('✅ New Academic Year:', current);
          setCurrentAcademicYear(current);
          if (typeof window !== 'undefined') {
            localStorage.setItem('currentAcademicYear', current); // Store the current academic year in localStorage
          }
        }
      } else {
        console.warn('⚠️ No current academic year found for today.');
        setCurrentAcademicYear(''); // Or you can keep it as the previous year if you have that saved in localStorage
      }
    } catch (error) {
      console.error('Error fetching academic years:', error);
    } finally {
      setYearLoading(false);
    }
  };

  useEffect(() => {
    // If no academic year in state, fetch it
    if (!currentAcademicYear) {
      fetchAcademicYear();
    }
  }, [currentAcademicYear]);

  return {
    currentAcademicYear,
    yearLoading,
    refreshAcademicYear: fetchAcademicYear,
  };
};

export default useAcademicYear;
