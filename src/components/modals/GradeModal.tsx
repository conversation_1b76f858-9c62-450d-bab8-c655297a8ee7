"use client";

import React, { useState, useEffect } from "react";
import { X, Percent, Save, Search, ChevronDown, Calendar } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { getAvailableTerms, GradeTerm, GradeSequence } from "@/app/services/GradeServices";
import useAuth from "@/app/hooks/useAuth";

interface GradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  grade?: any | null;
  students: any[];
  subjects: any[];
  examTypes: any[];
  loading?: boolean;
}

export default function GradeModal({
  isOpen,
  onClose,
  onSubmit,
  grade,
  students,
  subjects,
  examTypes,
  loading = false
}: GradeModalProps) {
  const { user } = useAuth();
  const schoolId:any = user?.school_ids?.[0] || user?.school_id;

  const [formData, setFormData] = useState({
    student_id: "",
    subject_id: "",
    subject: "" ,
    exam_type: "",
    term_id: "",
    sequence_number: 1,
    // Legacy fields for backward compatibility
    term: "",
    academic_year: "",
    score: "",
    grade: "",
    comments: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Terms state
  const [availableTerms, setAvailableTerms] = useState<GradeTerm[]>([]);
  const [currentTerm, setCurrentTerm] = useState<GradeTerm | null>(null);
  const [selectedTerm, setSelectedTerm] = useState<GradeTerm | null>(null);
  const [loadingTerms, setLoadingTerms] = useState(false);

  // Search states
  const [studentSearch, setStudentSearch] = useState('');
  const [subjectSearch, setSubjectSearch] = useState('');
  const [examTypeSearch, setExamTypeSearch] = useState('');
  const [showStudentDropdown, setShowStudentDropdown] = useState(false);
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [showExamTypeDropdown, setShowExamTypeDropdown] = useState(false);

  const isEditing = !!grade;

  // Load available terms when modal opens
  useEffect(() => {
    const loadTerms = async () => {
      if (!isOpen || !schoolId) return;

      try {
        setLoadingTerms(true);
        const termsData = await getAvailableTerms(schoolId);
        setAvailableTerms(termsData.terms);
        setCurrentTerm(termsData.current_term);

        // Auto-select current term if no term is selected and creating new grade
        if (!grade && termsData.current_term && !formData.term_id) {
          setSelectedTerm(termsData.current_term);
          setFormData(prev => ({
            ...prev,
            term_id: termsData.current_term!._id,
            term: termsData.current_term!.name,
            academic_year: termsData.current_term!.academic_year,
            sequence_number: termsData.current_term!.sequences[0]?.sequence_number || 1
          }));
        }
      } catch (error) {
        console.error('Error loading terms:', error);
      } finally {
        setLoadingTerms(false);
      }
    };

    loadTerms();
  }, [isOpen, schoolId]);

  useEffect(() => {
    if (isOpen) {
      if (grade) {
        console.log('Grade data for editing:', grade); // Debug log

        // Extract IDs more robustly
        let studentId = "";
        let subjectId = "";
        let examTypeId = "";

        // Handle student_id extraction
        if (grade.student_id) {
          if (typeof grade.student_id === 'object' && grade.student_id._id) {
            studentId = grade.student_id._id;
          } else if (typeof grade.student_id === 'string') {
            studentId = grade.student_id;
          }
        }

        // Handle subject_id extraction
        if (grade.subject_id) {
          if (typeof grade.subject_id === 'object' && grade.subject_id._id) {
            subjectId = grade.subject_id._id;
          } else if (typeof grade.subject_id === 'string') {
            subjectId = grade.subject_id;
          }
        }

        // Handle exam_type extraction
        if (grade.exam_type) {
          if (typeof grade.exam_type === 'object' && grade.exam_type._id) {
            examTypeId = grade.exam_type._id;
          } else if (typeof grade.exam_type === 'string') {
            examTypeId = grade.exam_type;
          }
        }

        // Convert percentage score to /20 for display
        const scoreOn20 = grade.score ? (grade.score).toFixed(1) : "";

        console.log('Extracted IDs:', { studentId, subjectId, examTypeId }); // Debug log

        // Find the corresponding term if editing
        let termForEditing = null;
        if (grade.term_id && availableTerms.length > 0) {
          termForEditing = availableTerms.find(t => t._id === grade.term_id);
        }

        setFormData({
          student_id: studentId,
          subject_id: subjectId,
          subject: grade.subject_name || "",
          exam_type: examTypeId,
          term_id: grade.term_id || "",
          sequence_number: grade.sequence_number || 1,
          // Legacy fields
          term: grade.term || "",
          academic_year: grade.academic_year || "",
          score: scoreOn20,
          grade: grade.grade || "",
          comments: grade.comments || ""
        });

        // Set search fields with the actual names for display
        const selectedStudent = students.find(s => s._id === studentId);
        const selectedSubject = subjects.find(s => s._id === subjectId);
        const selectedExamType = examTypes.find(e => e._id === examTypeId);

        if (selectedStudent) {
          const studentName = selectedStudent.first_name && selectedStudent.last_name
            ? `${selectedStudent.first_name} ${selectedStudent.last_name}`
            : selectedStudent.name || `Student ${selectedStudent._id}`;
          setStudentSearch(studentName);
        }

        if (selectedSubject) {
          setSubjectSearch(selectedSubject.name || selectedSubject.subject_name || `Subject ${selectedSubject._id}`);
        }

        if (selectedExamType) {
          setExamTypeSearch(selectedExamType.type || selectedExamType.name || `Exam Type ${selectedExamType._id}`);
        }

        if (termForEditing) {
          setSelectedTerm(termForEditing);
        }
      } else {
        // Reset form for new grade
        const defaultTerm = currentTerm || availableTerms[0];
        setFormData({
          student_id: "",
          subject_id: "",
          subject: "",
          exam_type: "",
          term_id: defaultTerm?._id || "",
          sequence_number: defaultTerm?.sequences[0]?.sequence_number || 1,
          // Legacy fields
          term: defaultTerm?.name || "",
          academic_year: defaultTerm?.academic_year || "",
          score: "",
          grade: "",
          comments: ""
        });

        if (defaultTerm) {
          setSelectedTerm(defaultTerm);
        }
      }
      setErrors({});
      // Reset search states
      setStudentSearch('');
      setSubjectSearch('');
      setExamTypeSearch('');
      setShowStudentDropdown(false);
      setShowSubjectDropdown(false);
      setShowExamTypeDropdown(false);
    }
  }, [isOpen, grade, availableTerms]);

  // Auto-calculate grade based on score (/20 system) with French/English mentions
  useEffect(() => {
    if (formData.score) {
      const score = parseFloat(formData.score);
      let calculatedGrade = "";

      // French/English grading system based on /20 score
      if (score >= 18) calculatedGrade = "A+/Excellent";
      else if (score >= 16) calculatedGrade = "A/Très bien";
      else if (score >= 14) calculatedGrade = "B+/Bien";
      else if (score >= 12) calculatedGrade = "B/Assez bien";
      else if (score >= 10) calculatedGrade = "C+/Passable";
      else if (score >= 8) calculatedGrade = "C/Insuffisant";
      else if (score >= 6) calculatedGrade = "D+/Médiocre";
      else if (score >= 4) calculatedGrade = "D/Très insuffisant";
      else calculatedGrade = "F/Nulle";

      setFormData(prev => ({ ...prev, grade: calculatedGrade }));
    }
  }, [formData.score]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Don't close if clicking inside any dropdown or search input
      if (target.closest('.dropdown-container') || target.closest('.search-input')) {
        return;
      }

      setShowStudentDropdown(false);
      setShowSubjectDropdown(false);
      setShowExamTypeDropdown(false);
    };

    if (showStudentDropdown || showSubjectDropdown || showExamTypeDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showStudentDropdown, showSubjectDropdown, showExamTypeDropdown]);

  // Handle term selection
  const handleTermChange = (termId: string) => {
    const term = availableTerms.find(t => t._id === termId);
    if (term) {
      setSelectedTerm(term);
      setFormData(prev => ({
        ...prev,
        term_id: termId,
        term: term.name,
        academic_year: term.academic_year,
        sequence_number: term.sequences[0]?.sequence_number || 1
      }));
    }
  };

  // Handle sequence selection
  const handleSequenceChange = (sequenceNumber: number) => {
    setFormData(prev => ({
      ...prev,
      sequence_number: sequenceNumber
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.student_id) {
      newErrors.student_id = "Student is required";
    }
    if (!formData.subject_id) {
      newErrors.subject_id = "Subject is required";
    }
    // Exam type is now optional since we have sequences
    if (!formData.term_id) {
      newErrors.term_id = "Term is required";
    }
    if (!formData.sequence_number) {
      newErrors.sequence_number = "Sequence is required";
    }
    if (!formData.score) {
      newErrors.score = "Score is required";
    } else {
      const score = parseFloat(formData.score);
      if (isNaN(score) || score < 0 || score > 20) {
        newErrors.score = "Score must be a number between 0 and 20";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const scoreOn20 = parseFloat(formData.score);

      // Build submit data, filtering out empty strings for updates
      const submitData: any = {
        score: scoreOn20,
        comments: formData.comments || ""
      };

      // Only include non-empty fields
      if (formData.student_id && formData.student_id !== "") {
        submitData.student_id = formData.student_id;
      }

      if (formData.subject_id && formData.subject_id !== "") {
        submitData.subject_id = formData.subject_id;
      }

      if (formData.exam_type && formData.exam_type !== "") {
        submitData.exam_type = formData.exam_type;
      }

      if (formData.term_id && formData.term_id !== "") {
        submitData.term_id = formData.term_id;
      }

      if (formData.sequence_number) {
        submitData.sequence_number = formData.sequence_number;
      }

      // Legacy fields - only include if new fields are not present
      if (!submitData.term_id) {
        if (formData.term && formData.term !== "") {
          submitData.term = formData.term;
        }

        if (formData.academic_year && formData.academic_year !== "") {
          submitData.academic_year = formData.academic_year;
        }
      }

      if (formData.grade && formData.grade !== "") {
        submitData.grade = formData.grade;
      }

      await onSubmit(submitData);
      onClose();
    } catch (error) {
      console.error("Error submitting grade:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Helper functions for search and selection
  const getSelectedStudentName = () => {
    const selectedStudent = students.find(s => s._id === formData.student_id);
    return selectedStudent ? `${selectedStudent.first_name} ${selectedStudent.last_name} (${selectedStudent.student_id})` : '';
  };

  const getSelectedSubjectName = () => {
    const selectedSubject = subjects.find(s => s._id === formData.subject_id);
    return selectedSubject ? selectedSubject.name : '';
  };

  const getSelectedExamTypeName = () => {
    const selectedExamType = examTypes.find(e => e._id === formData.exam_type);
    return selectedExamType ? selectedExamType.type : '';
  };

  const filteredStudents = students.filter(student =>
    `${student.first_name} ${student.last_name} ${student.student_id}`.toLowerCase().includes(studentSearch.toLowerCase())
  );

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(subjectSearch.toLowerCase())
  );

  const filteredExamTypes = examTypes.filter(examType =>
    examType.type.toLowerCase().includes(examTypeSearch.toLowerCase())
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={(e) => {
              const target = e.target as HTMLElement;
              // Don't close if clicking inside dropdown or search input
              if (target.closest('.dropdown-container') || target.closest('.search-input')) {
                return;
              }
              onClose();
            }}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Percent className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Grade" : "Add New Grade"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update grade record" : "Create new grade record"}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Student Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Student <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.student_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowStudentDropdown(!showStudentDropdown);
                      setShowSubjectDropdown(false);
                      setShowExamTypeDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedStudentName() || "Select student"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showStudentDropdown && (
                    <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search students..."
                            value={studentSearch}
                            onChange={(e) => setStudentSearch(e.target.value)}
                            className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredStudents.length > 0 ? (
                          filteredStudents.map((student) => (
                            <div
                              key={student._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("student_id", student._id);
                                setShowStudentDropdown(false);
                                setStudentSearch('');
                              }}
                            >
                              <div className="font-medium">{student.first_name} {student.last_name}</div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">{student.student_id}</div>
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No students found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.student_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.student_id}</p>
                )}
              </div>

              {/* Subject Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subject <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.subject_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowSubjectDropdown(!showSubjectDropdown);
                      setShowStudentDropdown(false);
                      setShowExamTypeDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedSubjectName() || "Select subject"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showSubjectDropdown && (
                    <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search subjects..."
                            value={subjectSearch}
                            onChange={(e) => setSubjectSearch(e.target.value)}
                            className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredSubjects.length > 0 ? (
                          filteredSubjects.map((subject) => (
                            <div
                              key={subject._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("subject_id", subject._id);
                                handleInputChange("subject", subject.name);
                                setShowSubjectDropdown(false);
                                setSubjectSearch('');
                              }}
                            >
                              {subject.name}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No subjects found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.subject_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                )}
              </div>

              {/* Exam Type Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Exam Type <span className="text-gray-400 text-xs">(Optional)</span>
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.exam_type
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowExamTypeDropdown(!showExamTypeDropdown);
                      setShowStudentDropdown(false);
                      setShowSubjectDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedExamTypeName() || "No exam type (Optional)"}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showExamTypeDropdown && (
                    <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search exam types..."
                            value={examTypeSearch}
                            onChange={(e) => setExamTypeSearch(e.target.value)}
                            className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredExamTypes.length > 0 ? (
                          filteredExamTypes.map((examType) => (
                            <div
                              key={examType._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("exam_type", examType._id);
                                setShowExamTypeDropdown(false);
                                setExamTypeSearch('');
                              }}
                            >
                              {examType.type}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No exam types found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.exam_type && (
                  <p className="mt-1 text-sm text-red-500">{errors.exam_type}</p>
                )}
              </div>

              {/* Term and Sequence Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Term <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    {loadingTerms ? (
                      <div className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-100 dark:bg-gray-700 flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500 mr-2"></div>
                        <span className="text-gray-500 dark:text-gray-400">Loading terms...</span>
                      </div>
                    ) : (
                      <>
                        <select
                          value={formData.term_id}
                          onChange={(e) => handleTermChange(e.target.value)}
                          className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white appearance-none ${
                            errors.term_id
                              ? "border-red-500 dark:border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          }`}
                        >
                          <option value="">Select a term</option>
                          {availableTerms.map((term) => (
                            <option key={term._id} value={term._id}>
                              {term.name} ({term.academic_year})
                              {term.is_current && " - Current"}
                            </option>
                          ))}
                        </select>
                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                      </>
                    )}
                  </div>
                  {errors.term_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.term_id}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sequence <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <select
                      value={formData.sequence_number}
                      onChange={(e) => handleSequenceChange(parseInt(e.target.value))}
                      className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white appearance-none ${
                        errors.sequence_number
                          ? "border-red-500 dark:border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      }`}
                      disabled={!selectedTerm}
                    >
                      <option value="">Select a sequence</option>
                      {selectedTerm?.sequences.map((sequence) => (
                        <option key={sequence.sequence_number} value={sequence.sequence_number}>
                          {sequence.sequence_name}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                  </div>
                  {errors.sequence_number && (
                    <p className="mt-1 text-sm text-red-500">{errors.sequence_number}</p>
                  )}
                </div>
              </div>

              {/* Academic Year Display */}
              {selectedTerm && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      Academic Year: {selectedTerm.academic_year}
                    </span>
                    {selectedTerm.is_current && (
                      <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 px-2 py-1 rounded-full">
                        Current Term
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Score and Grade */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Score (/20)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="20"
                    step="0.01"
                    value={formData.score}
                    onChange={(e) => handleInputChange("score", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                      errors.score
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="17.1"
                  />
                  {errors.score && (
                    <p className="mt-1 text-sm text-red-500">{errors.score}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Grade (Auto-calculated - French/English)
                  </label>
                  <input
                    type="text"
                    value={formData.grade}
                    onChange={(e) => handleInputChange("grade", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                    placeholder="A+/Excellent"
                  />
                </div>
              </div>

              {/* Comments */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Comments (Optional)
                </label>
                <textarea
                  value={formData.comments}
                  onChange={(e) => handleInputChange("comments", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Additional comments about the student's performance..."
                />
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
