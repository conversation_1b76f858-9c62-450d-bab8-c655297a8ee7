// components/AcademicYearCard.tsx

import { CalendarDays, RefreshCcw } from 'lucide-react';
import { useAcademicYearContext } from '@/context/AcademicYearContext';

const AcademicYearCard = () => {
  const { currentAcademicYear, yearLoading, refreshAcademicYear } = useAcademicYearContext();

  return (
    <div className="w-full max-w-sm p-4 bg-white shadow-md rounded-lg border border-gray-200 dark:bg-gray-900 dark:border-gray-700">
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-sm font-semibold text-gray-700 dark:text-gray-200 flex items-center gap-2">
          <CalendarDays className="w-5 h-5" />
          Academic Year
        </h2>
        <button
          onClick={refreshAcademicYear}
          className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition"
          title="Refresh"
        >
          <RefreshCcw className="w-4 h-4" />
        </button>
      </div>

      {yearLoading ? (
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-2/3 mb-1" />
          <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded w-1/3" />
        </div>
      ) : currentAcademicYear ? (
        <div>
          <p className="text-sm font-bold text-gray-900 dark:text-white">
            {currentAcademicYear}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Active academic session
          </p>
        </div>
      ) : (
        <div className="text-sm text-gray-500 dark:text-gray-400 italic">
          No active academic year found
        </div>
      )}
    </div>
  );
};

export default AcademicYearCard;
