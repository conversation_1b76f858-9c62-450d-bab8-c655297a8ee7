"use client";

import React, { useState, useEffect } from "react";
import { Megaphone, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";
import { getAnnouncementsByPriority, AnnouncementSchema } from "@/app/services/AnnouncementServices";
import CircularLoader from "@/components/widgets/CircularLoader";
import AnnouncementCard from "@/components/cards/AnnouncementCard";
import { motion } from "framer-motion";

interface RecentAnnouncementsProps {
  schoolId: string;
}

const RecentAnnouncements: React.FC<RecentAnnouncementsProps> = ({ schoolId }) => {
  const [announcements, setAnnouncements] = useState<AnnouncementSchema[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchPriorityAnnouncements = async () => {
      if (!schoolId) return;

      setLoading(true);
      setError(null);

      try {
        const priorityAnnouncements = await getAnnouncementsByPriority(schoolId, 5);
        setAnnouncements(priorityAnnouncements);
      } catch (err) {
        console.error("Error fetching priority announcements:", err);
        setError("Failed to load announcements");
      } finally {
        setLoading(false);
      }
    };

    fetchPriorityAnnouncements();
  }, [schoolId]);



  // Handler functions for announcement actions
  const handleView = (announcement: AnnouncementSchema) => {
    router.push(`/school-admin/announcements/view?id=${announcement.announcement_id}`);
  };

  const handleEdit = (announcement: AnnouncementSchema) => {
    router.push(`/school-admin/announcements?edit=${announcement.announcement_id}`);
  };

  const handleDelete = (announcement: AnnouncementSchema) => {
    router.push(`/school-admin/announcements?delete=${announcement.announcement_id}`);
  };

  const handleViewAll = () => {
    router.push('/school-admin/announcements');
  };

  if (loading) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <p className="text-foreground/60">{error}</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-stroke bg-widget p-4">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Megaphone className="h-5 w-5 text-[#17B890]" />
          <h3 className="text-lg font-semibold text-foreground">Priority Announcements</h3>
        </div>
        {announcements.length > 0 && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleViewAll}
            className="flex items-center gap-1 text-sm text-teal hover:text-teal-600 font-medium transition-colors"
          >
            View All
            <ArrowRight className="h-4 w-4" />
          </motion.button>
        )}
      </div>

      {announcements.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-8">
          <Megaphone className="h-12 w-12 text-foreground/30 mb-2" />
          <p className="text-foreground/60 text-center">No announcements available</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleViewAll}
            className="mt-3 text-sm text-teal hover:text-teal-600 font-medium"
          >
            Create your first announcement
          </motion.button>
        </div>
      ) : (
        <div className="space-y-3 max-h-[400px] overflow-y-auto custom-scrollbar">
          {announcements.map((announcement, index) => (
            <motion.div
              key={announcement._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <AnnouncementCard
                announcement={announcement}
                onView={handleView}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </motion.div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecentAnnouncements;
