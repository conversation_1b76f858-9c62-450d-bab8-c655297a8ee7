"use client";

import React, { Suspense, useEffect, useState } from "react";
import { Calendar, Plus, Filter, Download, TrendingUp, Users, BookOpen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import DataTableFix from "@/components/utils/TableFix";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { TermsSkeleton } from "@/components/skeletons";
import {
  getTermsBySchool,
  getCurrentTerm,
  createTerm,
  updateTerm,
  deleteTerm,
  setCurrentTerm,
  TermSchema,
  CreateTermData,
  UpdateTermData
} from "@/app/services/TermServices";
import TermModal from "./components/TermModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import { verifyPassword } from "@/app/services/UserServices";
import { useToast, ToastContainer } from "@/components/ui/Toast";

const BASE_URL = "/school-admin";

const navigation = {
  icon: Calendar,
  baseHref: `${BASE_URL}/terms`,
  title: "Terms Management"
};

export default function TermsPage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [terms, setTerms] = useState<TermSchema[]>([]);
  const [currentTerm, setCurrentTermData] = useState<TermSchema | null>(null);
  const [loading, setLoading] = useState(true);
  const [isTermModalOpen, setIsTermModalOpen] = useState(false);
  const [termToEdit, setTermToEdit] = useState<TermSchema | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTerms, setSelectedTerms] = useState<TermSchema[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [termToDelete, setTermToDelete] = useState<TermSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [clearSelection, setClearSelection] = useState(false);

  // Filters
  const [selectedAcademicYear, setSelectedAcademicYear] = useState<string>('all');

  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Get unique academic years for filter
  const academicYears = [...new Set(terms.map(term => term.academic_year))];

  // Fetch terms data from API
  useEffect(() => {
    const fetchTermsData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch terms and current term in parallel
        const [termsResponse, currentTermResponse] = await Promise.allSettled([
          getTermsBySchool(schoolId.toString(), selectedAcademicYear !== 'all' ? selectedAcademicYear : undefined),
          getCurrentTerm(schoolId.toString())
        ]);

        if (termsResponse.status === 'fulfilled') {
          setTerms(termsResponse.value.terms);
        } else {
          console.error("Error fetching terms:", termsResponse.reason);
          setTerms([]);
        }

        if (currentTermResponse.status === 'fulfilled') {
          setCurrentTermData(currentTermResponse.value.term);
        } else {
          console.log("No current term found");
          setCurrentTermData(null);
        }
      } catch (error) {
        console.error("Error fetching terms data:", error);
        showError("Error", "Failed to load terms data");
      } finally {
        setLoading(false);
      }
    };

    fetchTermsData();
  }, [schoolId, selectedAcademicYear]);

  // CRUD Functions
  const handleCreateTerm = () => {
    setTermToEdit(null);
    setIsTermModalOpen(true);
  };

  const handleEditTerm = (term: TermSchema) => {
    setTermToEdit(term);
    setIsTermModalOpen(true);
  };

  const handleDeleteTerm = (term: TermSchema) => {
    setTermToDelete(term);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: TermSchema[]) => {
    setSelectedTerms(selectedRows);
  };

  const handleTermSubmit = async (data: CreateTermData | UpdateTermData) => {
    setIsSubmitting(true);
    try {
      if (termToEdit) {
        // Update existing term
        await updateTerm(schoolId!.toString(), termToEdit._id, data as UpdateTermData);
        showSuccess("Success", "Term updated successfully");
      } else {
        // Create new term
        await createTerm(schoolId!.toString(), data as CreateTermData);
        showSuccess("Success", "Term created successfully");
      }

      // Refresh terms list
      const termsResponse = await getTermsBySchool(schoolId!.toString(), selectedAcademicYear !== 'all' ? selectedAcademicYear : undefined);
      setTerms(termsResponse.terms);
      
      setIsTermModalOpen(false);
      setTermToEdit(null);
    } catch (error: any) {
      showError("Error", error.message || "Failed to save term");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSetCurrentTerm = async (termId: string) => {
    try {
      await setCurrentTerm(schoolId!.toString(), termId);
      showSuccess("Success", "Current term updated successfully");
      
      // Refresh data
      const [termsResponse, currentTermResponse] = await Promise.all([
        getTermsBySchool(schoolId!.toString(), selectedAcademicYear !== 'all' ? selectedAcademicYear : undefined),
        getCurrentTerm(schoolId!.toString())
      ]);
      
      setTerms(termsResponse.terms);
      setCurrentTermData(currentTermResponse.term);
    } catch (error: any) {
      showError("Error", error.message || "Failed to set current term");
    }
  };

  const confirmDelete = async (password: string) => {
    try {
      // Verify password
      if (!user || !user.email) {
        showError("Error", "User not found or email missing");
        return;
      }
      await verifyPassword(password, user.email);

      if (deleteType === "single" && termToDelete) {
        await deleteTerm(schoolId!.toString(), termToDelete._id);
        showSuccess("Success", "Term deleted successfully");
      } else if (deleteType === "multiple" && selectedTerms.length > 0) {
        // Delete multiple terms
        await Promise.all(selectedTerms.map(term => deleteTerm(schoolId!.toString(), term._id)));
        showSuccess("Success", `${selectedTerms.length} terms deleted successfully`);
        setClearSelection(true);
      }

      // Refresh terms list
      const termsResponse = await getTermsBySchool(schoolId!.toString(), selectedAcademicYear !== 'all' ? selectedAcademicYear : undefined);
      setTerms(termsResponse.terms);
      
      setIsDeleteModalOpen(false);
      setTermToDelete(null);
    } catch (error: any) {
      showError("Error", error.message || "Failed to delete term(s)");
    }
  };

  // Table columns
  const columns = [
    { header: "Term Name", accessor: (row: TermSchema) => row.name },
    { header: "Term Number", accessor: (row: TermSchema) => `Term ${row.term_number}` },
    { header: "Academic Year", accessor: (row: TermSchema) => row.academic_year },
    { header: "Sequences", accessor: (row: TermSchema) => `${row.sequences.length} sequences` },
    { header: "Start Date", accessor: (row: TermSchema) => new Date(row.start_date).toLocaleDateString() },
    { header: "End Date", accessor: (row: TermSchema) => new Date(row.end_date).toLocaleDateString() },
    { 
      header: "Status", 
      accessor: (row: TermSchema) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          row.is_current 
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
            : row.is_active 
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
        }`}>
          {row.is_current ? 'Current' : row.is_active ? 'Active' : 'Inactive'}
        </span>
      )
    }
  ];

  const actions = [
    {
      label: "Edit",
      onClick: handleEditTerm
    },
    {
      label: "Set as Current",
      onClick: (term: TermSchema) => handleSetCurrentTerm(term._id)
    },
    {
      label: "Delete",
      onClick: handleDeleteTerm
    }
  ];

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["school_admin"]}>
        <SchoolLayout
          navigation={navigation}
          showGoPro={true}
          onLogout={logout}
        >
          <TermsSkeleton />
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin"]}>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Terms Management</h1>
              <p className="text-foreground/60">
                Manage academic terms and sequences for your school.
              </p>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleCreateTerm}
              className="bg-teal text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-teal-dark transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Term
            </motion.button>
          </div>

          {/* Current Term Info */}
          {currentTerm && (
            <div className="bg-widget p-4 rounded-lg border border-stroke">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Current Term</p>
                  <p className="text-xl font-bold text-foreground">
                    {currentTerm.name} ({currentTerm.academic_year})
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <select
              value={selectedAcademicYear}
              onChange={(e) => setSelectedAcademicYear(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
            >
              <option value="all">All Academic Years</option>
              {academicYears.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>

          {/* Terms Table */}
          <div className="bg-widget rounded-lg border border-stroke">
            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<TermSchema>
                data={terms}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={() => handleDeleteMultiple()}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
                idAccessor="_id"
                enableBulkActions={true}
                showCheckbox={true}
              />
            </Suspense>
          </div>
        </div>

        {/* Term Modal */}
        <TermModal
          isOpen={isTermModalOpen}
          onClose={() => {
            setIsTermModalOpen(false);
            setTermToEdit(null);
          }}
          onSubmit={handleTermSubmit}
          term={termToEdit}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <PasswordConfirmDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setTermToDelete(null);
          }}
          onConfirm={confirmDelete}
          title={deleteType === "single" ? "Delete Term" : "Delete Multiple Terms"}
          message={
            deleteType === "single"
              ? `Are you sure you want to delete "${termToDelete?.name}"? This action cannot be undone.`
              : `Are you sure you want to delete ${selectedTerms.length} terms? This action cannot be undone.`
          }
        />

        {/* Toast Container */}
        <ToastContainer toasts={toasts} removeToast={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
