"use client";

import { Users, UserPlus, Key, Eye, Shield } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";
import DataTableFix from "@/components/utils/TableFix";
import { useRouter } from "next/navigation";
import CreateStaffModal from "./components/CreateStaffModal";
import DeleteStaffModal from "./components/DeleteStaffModal";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { StaffPermissionGate } from "@/components/utils/PermissionGate";
import usePermissions from "@/app/hooks/usePermissions";
import { 
  StaffSchema,
  getStaffBySchool,
  deleteStaff,
  resetStaffPassword,
  generateAccessCode
} from "@/app/services/StaffServices";
import { verifyPassword } from "@/app/services/UserServices";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";

const BASE_URL = "/school-admin";

const navigation = {
  icon: Users,
  baseHref: `${BASE_URL}/staff`,
  title: "Staff Management"
};

export default function StaffPage() {
  const { logout, user } = useAuth();
  const { hasPermission } = usePermissions();
  const router = useRouter();

  // State management
  const [staff, setStaff] = useState<StaffSchema[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<StaffSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<StaffSchema | null>(null);
  const [staffToEdit, setStaffToEdit] = useState<StaffSchema | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const [tableKey, setTableKey] = useState(0);

  // Notification state
  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState<"success" | "error">("success");

  // Fetch staff data
  useEffect(() => {
    const fetchStaff = async () => {
      try {
        if (user && user.school_ids && user.school_ids.length > 0) {
          const schoolId = user.school_ids[0];
          const staffData = await getStaffBySchool(schoolId);
          setStaff(staffData);
          console.log("has get staff ", staffData);
        }
      } catch (error) {
        console.error("Error fetching staff:", error);
        setNotificationMessage("Failed to load staff data");
        setNotificationType("error");
        setIsNotificationCard(true);
      } finally {
        setLoadingData(false);
      }
    };

    if (user) {
      fetchStaff();
    }
  }, [user]);

  // Handle staff creation/update success
  const handleStaffSaved = (newStaff: StaffSchema, isEdit: boolean) => {
    if (isEdit) {
      setStaff(prev => prev.map(s => s._id === newStaff._id ? newStaff : s));
      setNotificationMessage("Staff member updated successfully!");
    } else {
      setStaff(prev => [newStaff, ...prev]);
      setNotificationMessage("Staff member created successfully!");
    }
    
    setNotificationType("success");
    setIsNotificationCard(true);
    setTableKey(prev => prev + 1);
  };

  // Handle delete staff
  const handleDelete = async (password: string) => {
    if (!staffToDelete || !user?.school_ids?.[0]) return;

    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      await deleteStaff(staffToDelete.staff_id || staffToDelete._id, user.school_ids[0]);
      setStaff(prev => prev.filter(s => s._id !== staffToDelete._id));
      setNotificationMessage("Staff member removed successfully!");
      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");

      // Close modal after success
      setTimeout(() => {
        setIsDeleteModalOpen(false);
        setStaffToDelete(null);
        setSubmitStatus(null);
      }, 2000);
    } catch (error) {
      console.error("Error deleting staff:", error);
      setNotificationMessage("Failed to remove staff member");
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle reset password
  const handleResetPassword = async (staff: StaffSchema) => {
    try {
      await resetStaffPassword(staff.staff_id || staff._id);
      setNotificationMessage(`Password reset email sent to ${staff.email}`);
      setNotificationType("success");
      setIsNotificationCard(true);
    } catch (error) {
      console.error("Error resetting password:", error);
      setNotificationMessage("Failed to send password reset email");
      setNotificationType("error");
      setIsNotificationCard(true);
    }
  };

  // Handle generate access code (for teachers)
  const handleGenerateAccessCode = async (staff: StaffSchema) => {
    if (!user?.school_ids?.[0]) return;

    try {
      const result = await generateAccessCode(staff._id, user.school_ids[0]);
      setNotificationMessage(`New access code generated: ${result.access_code}`);
      setNotificationType("success");
      setIsNotificationCard(true);
    } catch (error) {
      console.error("Error generating access code:", error);
      setNotificationMessage("Failed to generate access code");
      setNotificationType("error");
      setIsNotificationCard(true);
    }
  };

  // Get role badge color
  const getRoleBadge = (role: string) => {
    const colors = {
      school_admin: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      teacher: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      bursar: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      dean_of_studies: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      custom: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"
    };
    return colors[role as keyof typeof colors] || colors.custom;
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean) => {
    return isActive 
      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
  };

  // Format role display name
  const formatRoleName = (role: string) => {
    const roleNames = {
      school_admin: "School Admin",
      teacher: "Teacher",
      bursar: "Bursar",
      dean_of_studies: "Dean of Studies",
      custom: "Custom"
    };
    return roleNames[role as keyof typeof roleNames] || role;
  };

  // Table columns
  const columns = [
    { 
      header: "Name", 
      accessor: (row: StaffSchema) => (
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-teal rounded-full flex items-center justify-center text-white text-sm font-medium">
            {row.first_name?.[0]}{row.last_name?.[0]}
          </div>
          <div>
            <p className="font-medium text-foreground">{row.first_name} {row.last_name}</p>
            <p className="text-sm text-foreground/60">{row.email}</p>
          </div>
        </div>
      )
    },
    { 
      header: "Role", 
      accessor: (row: StaffSchema) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleBadge(row.role)}`}>
          {formatRoleName(row.role)}
        </span>
      )
    },
    { 
      header: "Phone", 
      accessor: (row: StaffSchema) => row.phone || "N/A"
    },
    {
      header: "Status",
      accessor: (row: StaffSchema) => (
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(row.is_staff_active)}`}>
            {row.is_staff_active ? "Active" : "Inactive"}
          </span>
          {row.firebaseUid && (
            <div className="flex items-center space-x-1" title="Synchronized with Firebase">
              <Shield className="h-3 w-3 text-green-500" />
              <span className="text-xs text-green-600 dark:text-green-400">FB</span>
            </div>
          )}
        </div>
      )
    },
    { 
      header: "Joined", 
      accessor: (row: StaffSchema) => 
        new Date(row.created_at).toLocaleDateString()
    },
  ];
  console.log("staff data === ", staff)
  // Table actions with permission checks
  const actions = [
    {
      label: "View",
      onClick: (staff: StaffSchema) => {
        router.push(`${BASE_URL}/staff/view?id=${staff._id || staff.staff_id}`);
      },
      condition: () => hasPermission('staff', 'view_staff_list')
    },
    {
      label: "Edit",
      onClick: (staff: StaffSchema) => {
        setStaffToEdit(staff);
        setIsCreateModalOpen(true);
      },
      condition: () => hasPermission('staff', 'add_edit_delete_staff')
    },
    {
      label: "Reset Password",
      onClick: (staff: StaffSchema) => {
        handleResetPassword(staff);
      },
      condition: () => hasPermission('staff', 'reset_staff_passwords')
    },
    {
      label: "Generate Code",
      onClick: (staff: StaffSchema) => {
        handleGenerateAccessCode(staff);
      },
      condition: (staff: StaffSchema) =>
        staff.role === 'teacher' && hasPermission('staff', 'add_edit_delete_staff')
    },
    {
      label: "Remove",
      onClick: (staff: StaffSchema) => {
        setStaffToDelete(staff);
        setIsDeleteModalOpen(true);
      },
      condition: () => hasPermission('staff', 'add_edit_delete_staff')
    },
  ];

  return (
    <ProtectedRoute allowedRoles={["admin", "school_admin"]}>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <Suspense fallback={<CircularLoader />}>
          <div className="space-y-6">
            {/* Notification */}
            {isNotificationCard && (
              <NotificationCard
                message={notificationMessage}
                type={notificationType}
                onClose={() => setIsNotificationCard(false)}
                isVisible={true}
              />
            )}

            {/* Header with Add Button */}
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-foreground">Staff Management</h1>
                <p className="text-foreground/60">Manage school staff members and their permissions</p>
              </div>
              
              <StaffPermissionGate action="add_edit_delete_staff">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                  onClick={() => setIsCreateModalOpen(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
                >
                  <UserPlus size={16} />
                  <span>Add New Staff</span>
                </motion.button>
              </StaffPermissionGate>
            </div>

            {/* Modals */}
            {isCreateModalOpen && (
              <CreateStaffModal
                onClose={() => {
                  setIsCreateModalOpen(false);
                  setStaffToEdit(null);
                  setSubmitStatus(null);
                }}
                onSave={handleStaffSaved}
                initialData={staffToEdit}
                schoolId={user?.school_ids?.[0] || ""}
              />
            )}

            {isDeleteModalOpen && staffToDelete && (
              <DeleteStaffModal
                staffName={`${staffToDelete.first_name} ${staffToDelete.last_name}`}
                onClose={() => {
                  setIsDeleteModalOpen(false);
                  setStaffToDelete(null);
                  setSubmitStatus(null);
                }}
                onDelete={handleDelete}
                submitStatus={submitStatus}
                isSubmitting={isSubmitting}
              />
            )}

            {/* Data Table */}
            <DataTableFix<StaffSchema>
              key={tableKey}
              columns={columns}
              data={staff}
              actions={actions}
              defaultItemsPerPage={10}
              loading={loadingData}
              onLoadingChange={setLoadingData}
              onSelectionChange={setSelectedStaff}
              idAccessor="_id"
              enableBulkActions={false} // Disable bulk actions for staff
            />
          </div>
        </Suspense>
      </SchoolLayout>
    </ProtectedRoute>
  );
}
