"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { School, Key, LogIn, AlertCircle, Loader2 } from "lucide-react";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";
import Logo from "@/components/widgets/Logo";
import { getSchoolBy_id } from "@/app/services/SchoolServices";

interface SchoolAccess {
  school_id: string;
  access_code: string;
  granted_at: Date;
  granted_by: string;
  is_active: boolean;
  school_name?: string;
}

export default function TeacherDashboardPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  
  const [schools, setSchools] = useState<SchoolAccess[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSchool, setSelectedSchool] = useState<string>("");
  const [accessCode, setAccessCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAccessCodeInput, setShowAccessCodeInput] = useState(false);
  
  useEffect(() => {
    const fetchSchools = async () => {
      if (user && user.role === "teacher") {
        // Extract schools from user's access_codes
        const userSchools = (user.access_codes as any[]) || [];
        if(userSchools.length > 0){
          // Créer un nouveau tableau avec les noms d'école
          const schoolsWithNames = await Promise.all(
            userSchools.map(async (school) => {
              try {
                const schoolData = await getSchoolBy_id(school.school_id);
                console.log(`✅ Fetched school data for ${school.school_id}:`, schoolData);
                return {
                  ...school,
                  school_name: schoolData.name || schoolData.school_name || 'Unknown School'
                };
              } catch (error) {
                console.error(`❌ Error fetching school ${school.school_id}:`, error);
                return {
                  ...school,
                  school_name: 'Unknown School' // Better fallback
                };
              }
            })
          );

          setSchools(schoolsWithNames as SchoolAccess[]);
        } else {
          setSchools([]);
        }
        setLoading(false);
      } else if (user && user.role !== "teacher") {
        // Redirect non-teachers
        router.push("/dashboard");
      }
    };

    fetchSchools();
  }, [user, router]);

  const handleSchoolSelect = (schoolId: string) => {
    setSelectedSchool(schoolId);
    setShowAccessCodeInput(true);
    setError(null);
  };

  const handleAccessCodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!selectedSchool || !accessCode) {
      setError("Please select a school and enter the access code");
      return;
    }

    setError(null);

    setTimeout(() => {
      try {
        // Find the school access
        const schoolAccess = schools.find(s => s.school_id === selectedSchool);

        if (!schoolAccess) {
          setError("School not found in your access list");
          setIsSubmitting(false);
          return;
        }

        // Verify access code
        if (schoolAccess.access_code.toLowerCase() !== accessCode.toLowerCase()) {
          setError("Invalid access code");
          setIsSubmitting(false);
          return;
        }

        if (!schoolAccess.is_active) {
          setError("Your access to this school has been revoked");
          setIsSubmitting(false);
          return;
        }

        // Store selected school in localStorage
        const schoolToStore = {
          school_id: selectedSchool,
          school_name: schoolAccess.school_name || "Unknown School",
          access_granted_at: new Date().toISOString()
        };

        console.log('💾 Storing school in localStorage:', schoolToStore);
        localStorage.setItem("teacher_selected_school", JSON.stringify(schoolToStore));

        // Redirect to teacher dashboard
        router.push("/teacher-dashboard/dashboard");
        setIsSubmitting(false);
      } catch (error) {
        console.error("Error verifying access code:", error);
        setError("Failed to verify access code. Please try again.");
        setIsSubmitting(false);
      }
    }, 1000);
  };

  const handleBack = () => {
    setShowAccessCodeInput(false);
    setSelectedSchool("");
    setAccessCode("");
    setError(null);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <Logo />
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              Welcome, {user?.first_name || user?.name}!
            </h1>
            <p className="text-foreground/60">
              Select a school to access your teaching dashboard
            </p>
          </div>

          {/* Error Notification */}
          {error && (
            <div className="mb-6">
              <NotificationCard
                message={error}
                type="error"
                onClose={() => setError(null)}
                isVisible={true}
              />
            </div>
          )}

          {/* School Selection */}
          {!showAccessCodeInput ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-widget rounded-lg border border-stroke p-6 shadow-lg"
            >
              <div className="flex items-center space-x-2 mb-4">
                <School className="h-5 w-5 text-teal" />
                <h2 className="text-lg font-semibold text-foreground">Your Schools</h2>
              </div>

              {schools.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
                  <p className="text-foreground/60 mb-4">
                    You don't have access to any schools yet.
                  </p>
                  <p className="text-sm text-foreground/50">
                    Contact your school administrator to get access codes.
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {schools.map((school) => (
                    <motion.button
                      key={school.school_id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleSchoolSelect(school.school_id)}
                      disabled={!school.is_active}
                      className={`w-full p-4 border-2 rounded-lg text-left transition-all ${
                        school.is_active
                          ? "border-stroke hover:border-teal bg-widget hover:bg-teal/5"
                          : "border-red-200 bg-red-50 dark:bg-red-900/20 opacity-60 cursor-not-allowed"
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground">
                            {school.school_name || `School ${school.school_id.slice(-6)}`}
                          </p>
                          <p className="text-sm text-foreground/60">
                            Access granted: {new Date(school.granted_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {school.is_active ? (
                            <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs rounded-full">
                              Active
                            </span>
                          ) : (
                            <span className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 text-xs rounded-full">
                              Revoked
                            </span>
                          )}
                          <LogIn className="h-4 w-4 text-foreground/40" />
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              )}

              {/* Logout Button */}
              <div className="mt-6 pt-4 border-t border-stroke">
                <button
                  onClick={logout}
                  className="w-full px-4 py-2 text-foreground/70 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                >
                  Logout
                </button>
              </div>
            </motion.div>
          ) : (
            /* Access Code Input */
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-widget rounded-lg border border-stroke p-6 shadow-lg"
            >
              <div className="flex items-center space-x-2 mb-4">
                <Key className="h-5 w-5 text-teal" />
                <h2 className="text-lg font-semibold text-foreground">Enter Access Code</h2>
              </div>

              <p className="text-foreground/60 mb-6">
                Please enter your access code for the selected school to continue.
              </p>

              <form onSubmit={handleAccessCodeSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Access Code
                  </label>
                  <input
                    type="text"
                    value={accessCode}
                    onChange={(e) => setAccessCode(e.target.value.toUpperCase())}
                    placeholder="Enter your access code"
                    className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground font-mono text-center text-lg tracking-wider"
                    maxLength={16}
                    required
                    autoFocus
                  />
                </div>

                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleBack}
                    className="flex-1 px-4 py-2 text-foreground/70 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    disabled={isSubmitting}
                  >
                    Back
                  </button>
                  <button
                      type="submit"
                      disabled={isSubmitting || !accessCode}
                      className="flex-1 px-4 py-2 bg-teal text-sm text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 transition-all duration-200"
                  >
                    {isSubmitting && <Loader2 className="h-4 w-4 animate-spin" />}
                    {!isSubmitting && <LogIn className="h-4 w-4 mx-2" />}
                    Access Dashboard
                  </button>

                </div>
              </form>
            </motion.div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}
