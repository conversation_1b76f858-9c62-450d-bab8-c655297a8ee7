import { getToken<PERSON>rom<PERSON><PERSON>ie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

// New Teacher Assignment interfaces (for school admin management)
export interface TeacherAssignment extends Record<string, unknown> {
  _id: string;
  assignment_id: string;
  school_id: string;
  teacher_id: {
    _id: string;
    first_name: string;
    last_name: string;
    name: string;
    email: string;
  };
  class_id: {
    _id: string;
    name: string;
    class_code: string;
    level: string;
  };
  subjects: string[];
  academic_year: string;
  is_active: boolean;
  assigned_by: {
    _id: string;
    first_name: string;
    last_name: string;
    name: string;
  };
  assigned_at: string;
  last_modified_at: string;
}

export interface TeacherAssignmentCreate {
  school_id: string;
  teacher_id: string;
  class_id: string;
  subjects: string[];
  academic_year: string;
}

export interface TeacherAssignmentUpdate {
  subjects?: string[];
  academic_year?: string;
}

export interface AvailableTeacher {
  _id: string;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  display_name: string;
}

// Legacy interface for teacher dashboard (from StaffPermission)
export interface LegacyTeacherAssignment {
  _id: string;
  teacher_id: string;
  school_id: string;
  assigned_classes: Array<{
    _id: string;
    name: string;
    level: string;
  }>;
  assigned_subjects: Array<{
    _id: string;
    name: string;
    code: string;
  }>;
}

// ===== NEW TEACHER ASSIGNMENT FUNCTIONS (School Admin) =====

// Create teacher assignment
export async function createTeacherAssignment(
  schoolId: string,
  assignmentData: Omit<TeacherAssignmentCreate, 'school_id'>
): Promise<{ message: string; assignment: TeacherAssignment }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/school/${schoolId}/assignments`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        ...assignmentData,
        school_id: schoolId
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating teacher assignment:", errorData);
      throw new Error(errorData.message || "Failed to create teacher assignment");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create teacher assignment error:", error);
    throw error;
  }
}

// Get teacher assignments for a school
export async function getTeacherAssignmentsBySchool(
  schoolId: string,
  filters?: {
    teacher_id?: string;
    class_id?: string;
    academic_year?: string;
  }
): Promise<{ assignments: TeacherAssignment[]; total: number }> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    if (filters?.teacher_id) queryParams.append('teacher_id', filters.teacher_id);
    if (filters?.class_id) queryParams.append('class_id', filters.class_id);
    if (filters?.academic_year) queryParams.append('academic_year', filters.academic_year);

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/teacher/school/${schoolId}/assignments${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher assignments:", response.statusText);
      throw new Error("Failed to fetch teacher assignments");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch teacher assignments error:", error);
    throw error;
  }
}

// Update teacher assignment
export async function updateTeacherAssignment(
  assignmentId: string,
  updateData: TeacherAssignmentUpdate
): Promise<{ message: string; assignment: TeacherAssignment }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/assignments/${assignmentId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating teacher assignment:", errorData);
      throw new Error(errorData.message || "Failed to update teacher assignment");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update teacher assignment error:", error);
    throw error;
  }
}

// Delete teacher assignment
export async function deleteTeacherAssignment(assignmentId: string): Promise<{ message: string }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/assignments/${assignmentId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting teacher assignment:", errorData);
      throw new Error(errorData.message || "Failed to delete teacher assignment");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete teacher assignment error:", error);
    throw error;
  }
}

// Get teachers available for assignment
export async function getAvailableTeachers(schoolId: string): Promise<AvailableTeacher[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/school/${schoolId}/teachers`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching available teachers:", response.statusText);
      throw new Error("Failed to fetch available teachers");
    }

    const data = await response.json();
    return data.teachers || [];
  } catch (error) {
    console.error("Fetch available teachers error:", error);
    throw error;
  }
}

// ===== LEGACY FUNCTIONS (Teacher Dashboard) =====

// Get teacher's assigned classes and subjects for a specific school (from StaffPermission)
export async function getTeacherAssignments(schoolId: string): Promise<LegacyTeacherAssignment> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/assignments/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher assignments:", response.statusText);
      throw new Error("Failed to fetch teacher assignments");
    }

    const data = await response.json();
    return data as LegacyTeacherAssignment;
  } catch (error) {
    console.error("Fetch teacher assignments error:", error);
    throw new Error("Failed to fetch teacher assignments");
  }
}

// Get classes assigned to the current teacher
export async function getTeacherClasses(schoolId: string): Promise<Array<{_id: string, name: string, level: string}>> {
  try {
    const assignments = await getTeacherAssignments(schoolId);
    return assignments.assigned_classes;
  } catch (error) {
    console.error("Error fetching teacher classes:", error);
    return [];
  }
}

// Get subjects assigned to the current teacher
export async function getTeacherSubjects(schoolId: string): Promise<Array<{_id: string, name: string, code: string}>> {
  try {
    const assignments = await getTeacherAssignments(schoolId);
    return assignments.assigned_subjects;
  } catch (error) {
    console.error("Error fetching teacher subjects:", error);
    return [];
  }
}

// Get students in teacher's assigned classes
export async function getTeacherStudents(schoolId: string): Promise<Array<{
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  class_id: string;
  class_name: string;
}>> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/students/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher students:", response.statusText);
      throw new Error("Failed to fetch teacher students");
    }

    const data = await response.json();
    return data.students || [];
  } catch (error) {
    console.error("Fetch teacher students error:", error);
    return [];
  }
}

// Get teacher assigned to a specific class and subject
export async function getTeacherForClassSubject(
  schoolId: string,
  classId: string,
  subjectIdOrName: string
): Promise<{
  teacher_id: string | null;
  teacher_name: string;
  teacher_email: string | null;
} | null> {
  try {
    // Get all teacher assignments for the school
    const response = await getTeacherAssignmentsBySchool(schoolId, { class_id: classId });

    // Get all subjects to map IDs to names if needed
    const { getSubjectsBySchoolId } = await import("./SubjectServices");
    const allSubjects = await getSubjectsBySchoolId(schoolId);

    // Find the subject name from ID if an ID was passed
    let subjectName = subjectIdOrName;
    const subject = allSubjects.find((s: any) => s._id === subjectIdOrName);
    if (subject) {
      subjectName = subject.name;
    }

    // Find assignment that includes the subject
    const assignment = response.assignments.find(assignment =>
      assignment.subjects.includes(subjectName)
    );

    if (!assignment) {
      return null;
    }

    const teacherName = assignment.teacher_id.first_name && assignment.teacher_id.last_name
      ? `${assignment.teacher_id.first_name} ${assignment.teacher_id.last_name}`
      : assignment.teacher_id.name;

    return {
      teacher_id: assignment.teacher_id._id,
      teacher_name: teacherName,
      teacher_email: assignment.teacher_id.email
    };
  } catch (error) {
    console.error("Fetch teachers for class and subject error:", error);
    return null;
  }
}

// Get all teacher assignments for a class (grouped by subject)
export async function getTeacherAssignmentsByClass(
  schoolId: string,
  classId: string
): Promise<Array<{
  subject_id: string;
  subject_name: string;
  primary_teacher: string;
  teacher_id: string;
  teacher_email: string;
}>> {
  try {
    // Get all teacher assignments for the school filtered by class
    const response = await getTeacherAssignmentsBySchool(schoolId, { class_id: classId });

    // Get all subjects for the school to map names to IDs
    const { getSubjectsBySchoolId } = await import("./SubjectServices");
    const allSubjects = await getSubjectsBySchoolId(schoolId);

    // Create a map of subject names to IDs
    const subjectNameToId = new Map();
    allSubjects.forEach((subject: any) => {
      subjectNameToId.set(subject.name, subject._id);
    });

    // Transform assignments to the expected format
    const subjectAssignments: Array<{
      subject_id: string;
      subject_name: string;
      primary_teacher: string;
      teacher_id: string;
      teacher_email: string;
    }> = [];

    response.assignments.forEach(assignment => {
      assignment.subjects.forEach(subjectName => {
        const teacherName = assignment.teacher_id.first_name && assignment.teacher_id.last_name
          ? `${assignment.teacher_id.first_name} ${assignment.teacher_id.last_name}`
          : assignment.teacher_id.name;

        // Get the actual subject ID from the name
        const subjectId = subjectNameToId.get(subjectName) || subjectName;

        subjectAssignments.push({
          subject_id: subjectId, // Use actual subject ID
          subject_name: subjectName,
          primary_teacher: teacherName,
          teacher_id: assignment.teacher_id._id,
          teacher_email: assignment.teacher_id.email
        });
      });
    });

    return subjectAssignments;
  } catch (error) {
    console.error("Fetch teacher assignments for class error:", error);
    return [];
  }
}
