import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { ClassCreateSchema, ClassSchema, ClassUpdateSchema } from "../models/ClassModel";
import { getStudentsByClassAndSchool } from "./StudentServices";

export interface TopClassData {
  id: string;
  className: string;
  metrics: {
    "Number of Students": number;
    "Average Grade": number;
  };
}

export interface ClassGradeAverage {
  class_id: string;
  class_name: string;
  average_grade: number;
  student_count: number;
}

export interface GradeFilters {
  term_id?: string;
  sequence_number?: number;
  academic_year?: string;
}

// Get all classes
export async function getClasses(): Promise<ClassSchema[]> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(`${BASE_API_URL}/class/get-classes`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            throw new Error("Failed to fetch class data");
        }

        const classesList = await response.json();
        return classesList.map((cls: any) => ({
            _id: cls._id,
            class_id: cls.class_id,
            name: cls.name,
            school_id: cls.school_id,
            class_level: cls.class_level,
            class_code: cls.class_code,
            subject_id:cls.subject_id,
            createdAt: cls.createdAt,
            updatedAt: cls.updatedAt,
        })) as ClassSchema[];

    } catch (error) {
        console.error("Error fetching classes:", error);
        throw new Error("Failed to fetch class data");
    }
}

// Get single class by ID
export async function getClassById(classId: string): Promise<ClassSchema> {
    const response = await fetch(`${BASE_API_URL}/class/get-class/${classId}`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });

    if (!response.ok) {
        throw new Error("Failed to fetch class");
    }

    const data = await response.json();
    return {
        _id: data._id,
        class_id: data.class_id,
        name: data.name,
        school_id: data.school_id,
        class_level: data.class_level,
        class_code: data.class_code,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
    } as ClassSchema;
}

// Create class
export async function createClass(classData: ClassCreateSchema) {
    try {
        const response = await fetch(`${BASE_API_URL}/class/create-class`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
            },
            body: JSON.stringify(classData),
        });

        if (!response.ok) {
            let errorMessage = "Failed to create class";
            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }
            throw new Error(errorMessage);
        }

        return await response.json();
    } catch (error) {
        console.error("Error creating class:", error);
        throw new Error(error instanceof Error ? error.message : "Failed to create class");
    }
}

// Update class
export async function updateClass(classId: string, classData: ClassUpdateSchema) {
    try {
        const response = await fetch(`${BASE_API_URL}/class/update-class/${classId}`, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
            },
            body: JSON.stringify(classData),
        });

        if (!response.ok) {
            let errorMessage = "Failed to update class";
            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }
            throw new Error(errorMessage);
        }

        return await response.json();
    } catch (error) {
        console.error("Error updating class:", error);
        throw new Error(error instanceof Error ? error.message : "Failed to update class");
    }
}

// Delete class
export async function deleteClass(classId: string) {
    try {
        const response = await fetch(`${BASE_API_URL}/class/delete-class/${classId}`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
            },
        });

        if (!response.ok) {
            let errorMessage = "Failed to delete class";
            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }
            throw new Error(errorMessage);
        }

        return await response.json();
    } catch (error) {
        console.error("Error deleting class:", error);
        throw new Error(error instanceof Error ? error.message : "Failed to delete class");
    }
}

// Delete multiple classes
export async function deleteMultipleClasses(classIds: string[]) {
    try {
        const response = await fetch(`${BASE_API_URL}/class/delete-classes`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
            },
            body: JSON.stringify({ ids: classIds }),
        });

        if (!response.ok) {
            let errorMessage = "Failed to delete multiple classes";
            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }
            throw new Error(errorMessage);
        }

        return await response.json();
    } catch (error) {
        console.error("Error deleting multiple classes:", error);
        throw new Error(error instanceof Error ? error.message : "Failed to delete multiple classes");
    }
}

// Delete all classes
export async function deleteAllClasses() {
    try {
        const response = await fetch(`${BASE_API_URL}/class/delete-all-classes`, {
            method: "DELETE",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
            },
        });

        if (!response.ok) {
            let errorMessage = "Failed to delete all classes";
            try {
                const errorBody = await response.json();
                errorMessage = errorBody?.message || errorMessage;
            } catch (parseError) {
                console.warn("Could not parse error response:", parseError);
            }
            throw new Error(errorMessage);
        }

        return await response.json();
    } catch (error) {
        console.error("Error deleting all classes:", error);
        throw new Error(error instanceof Error ? error.message : "Failed to delete all classes");
    }
}

// Get classes by school
export async function getClassesBySchool(schoolId: string): Promise<ClassSchema[]> {
    try {
        const token = getTokenFromCookie("idToken");
        const response = await fetch(`${BASE_API_URL}/class/get-classes`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            throw new Error("Failed to fetch class data");
        }

        const classesList = await response.json();
        console.log(classesList, schoolId);
        // Filter classes by school_id
        return classesList
            .filter((cls: any) => cls.school_id === schoolId)
            .map((cls: any) => ({
                _id: cls._id,
                class_id: cls.class_id,
                name: cls.name,
                school_id: cls.school_id,
                class_level: cls.class_level,
                class_code: cls.class_code,
                createdAt: cls.createdAt,
                updatedAt: cls.updatedAt,
            })) as ClassSchema[];

    } catch (error) {
        console.error("Error fetching classes by school:", error);
        throw new Error("Failed to fetch class data by school");
    }
}

// Get top classes by school with performance metrics
export async function getTopClassesBySchool(schoolId: string): Promise<TopClassData[]> {
    try {
        const classes = await getClassesBySchool(schoolId);
        const topClassesData: TopClassData[] = [];
        // For each class, get student count and calculate average grade
        for (const classItem of classes) {
            try {
                console.log("before ")
                const students :any = [] //await getStudentsByClassAndSchool(classItem.class_id, schoolId);
                const studentCount = students.length;
                console.log("after ", students, classItem.name);
                // Calculate average grade (mock data for now, can be replaced with real grade calculation)
                const averageGrade = Math.floor(Math.random() * (95 - 70) + 70); // Random grade between 70-95

                topClassesData.push({
                    id: classItem.class_id,
                    className: classItem.name,
                    metrics: {
                        "Number of Students": studentCount,
                        "Average Grade": averageGrade,
                    },
                });
            } catch (error) {
                console.warn(`Error fetching data for class ${classItem.name}:`, error);
                // Add class with default values if error occurs
                topClassesData.push({
                    id: classItem.class_id,
                    className: classItem.name,
                    metrics: {
                        "Number of Students": 0,
                        "Average Grade": 0,
                    },
                });
            }
        }

        // Sort by average grade and return top 5
        return topClassesData
            .sort((a, b) => b.metrics["Average Grade"] - a.metrics["Average Grade"])
            .slice(0, 5);

    } catch (error) {
        console.error("Error fetching top classes by school:", error);
        throw new Error("Failed to fetch top classes data");
    }
}

// Get top classes by average grades with term and sequence filters
export async function getTopClassesByGrades(schoolId: string, filters: GradeFilters = {}): Promise<ClassGradeAverage[]> {
    try {
        const token = getTokenFromCookie("idToken");

        // Build query parameters
        const queryParams = new URLSearchParams();
        if (filters.term_id) queryParams.append('term_id', filters.term_id);
        if (filters.sequence_number) queryParams.append('sequence_number', filters.sequence_number.toString());
        if (filters.academic_year) queryParams.append('academic_year', filters.academic_year);

        const queryString = queryParams.toString();
        const url = `${BASE_API_URL}/grades/school/${schoolId}/class-averages${queryString ? `?${queryString}` : ''}`;

        const response = await fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            throw new Error("Failed to fetch class grade averages");
        }

        const data = await response.json();

        // Sort by average grade (descending) and return top 5
        return data.class_averages
            .sort((a: ClassGradeAverage, b: ClassGradeAverage) => b.average_grade - a.average_grade)
            .slice(0, 5);

    } catch (error) {
        console.error("Error fetching top classes by grades:", error);
        throw new Error("Failed to fetch class grade averages");
    }
}
