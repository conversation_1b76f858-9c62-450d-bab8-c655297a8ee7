import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { ExamTypeSchema, ExamTypeCreateSchema, ExamTypeUpdateSchema } from "../models/ExampType";

// Get all exam types
export async function getExamTypes(): Promise<ExamTypeSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/exam/exam-types`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch exam types");
    }

    const examTypes = await response.json();
    return examTypes.map((examType: any) => ({
      _id: examType._id,
      school_id: examType.school_id,
      type: examType.type,
      createdAt: examType.createdAt,
      updatedAt: examType.updatedAt,
    })) as ExamTypeSchema[];
  } catch (error) {
    console.error("Error fetching exam types:", error);
    throw new Error("Failed to fetch exam types");
  }
}

// Get all exam types by school ID
export async function getExamTypesBySchoolId(schoolId: string): Promise<ExamTypeSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/exam/exam-types-by-school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch exam types for the school");
    }

    const examTypes = await response.json();
    return examTypes.map((examType: any) => ({
      _id: examType._id,
      school_id: examType.school_id,
      type: examType.type,
      createdAt: examType.createdAt,
      updatedAt: examType.updatedAt,
    })) as ExamTypeSchema[];
  } catch (error) {
    console.error("Error fetching exam types by school ID:", error);
    throw new Error("Failed to fetch exam types by school ID");
  }
}

// Get a single exam type by ID
export async function getExamTypeById(examTypeId: string): Promise<ExamTypeSchema> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/exam/exam-types/${examTypeId}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch exam type");
  }

  const examType = await response.json();
  return {
    _id: examType._id,
    school_id: examType.school_id,
    type: examType.type,
    createdAt: examType.createdAt,
    updatedAt: examType.updatedAt,
  };
}

// Create a new exam type
export async function createExamType(examTypeData: ExamTypeCreateSchema): Promise<ExamTypeSchema> {
  try {
    const response = await fetch(`${BASE_API_URL}/exam/exam-types`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(examTypeData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to create exam type");
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating exam type:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to create exam type");
  }
}

// Update an exam type
export async function updateExamType(examTypeId: string, examTypeData: ExamTypeUpdateSchema): Promise<ExamTypeSchema> {
  try {
    const response = await fetch(`${BASE_API_URL}/exam/exam-types/${examTypeId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify(examTypeData),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to update exam type");
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating exam type:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to update exam type");
  }
}

// Delete an exam type
export async function deleteExamType(examTypeId: string): Promise<void> {
  try {
    const response = await fetch(`${BASE_API_URL}/exam/exam-types/${examTypeId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete exam type");
    }
  } catch (error) {
    console.error("Error deleting exam type:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete exam type");
  }
}

// Delete multiple exam types
export async function deleteMultipleExamTypes(examTypeIds: string[]): Promise<void> {
  try {
    const response = await fetch(`${BASE_API_URL}/exam/delete-exam-types`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
      },
      body: JSON.stringify({ ids: examTypeIds }),
    });

    if (!response.ok) {
      const errorBody = await response.json();
      throw new Error(errorBody.message || "Failed to delete exam types");
    }
  } catch (error) {
    console.error("Error deleting multiple exam types:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to delete exam types");
  }
}
