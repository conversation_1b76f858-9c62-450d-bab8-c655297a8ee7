const mongoose = require('mongoose');
const NotificationService = require('./src/services/notificationService');
const Notification = require('./src/models/Notification');

// Simple test without requiring existing data
async function simpleNotificationTest() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify');
    console.log('✅ Connected to MongoDB');

    // Create a mock user ID and school ID
    const mockUserId = new mongoose.Types.ObjectId();
    const mockSchoolId = new mongoose.Types.ObjectId();
    const mockSenderId = new mongoose.Types.ObjectId();

    console.log('\n🧪 Test 1: Creating a simple notification...');
    
    // Test creating a notification directly
    const testNotification = await NotificationService.createNotification({
      recipient_id: mockUserId,
      school_id: mockSchoolId,
      type: 'success',
      category: 'staff',
      title: '🎉 Staff Creation Test',
      message: 'This is a test notification for staff creation functionality.',
      sender_id: mockSenderId,
      sender_type: 'user',
      action_url: '/school-admin/staff',
      action_label: 'View Staff',
      priority: 'normal',
      channels: {
        in_app: true,
        email: false,
        sms: false
      }
    });

    console.log('✅ Notification created successfully!');
    console.log(`   📧 ID: ${testNotification.notification_id}`);
    console.log(`   📝 Title: ${testNotification.title}`);
    console.log(`   📅 Created: ${testNotification.createdAt}`);

    console.log('\n🧪 Test 2: Testing staff creation notification simulation...');
    
    // Simulate the staff creation notification flow
    const mockStaff = {
      _id: new mongoose.Types.ObjectId(),
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>'
    };

    // Create welcome notification for new staff
    const welcomeNotification = await NotificationService.createNotification({
      recipient_id: mockStaff._id,
      school_id: mockSchoolId,
      type: 'success',
      category: 'staff',
      title: 'Welcome to the Team! 🎉',
      message: `Welcome John Doe! Your account has been created as a teacher. Please check your email for login instructions.`,
      sender_id: mockSenderId,
      sender_type: 'user',
      action_url: '/login',
      action_label: 'Login Now',
      priority: 'high',
      channels: {
        in_app: true,
        email: true
      }
    });

    // Create admin notification about new staff
    const adminNotification = await NotificationService.createNotification({
      recipient_id: mockUserId,
      school_id: mockSchoolId,
      type: 'info',
      category: 'staff',
      title: 'New Staff Member Added',
      message: `John Doe has been added as a teacher.`,
      sender_id: mockSenderId,
      sender_type: 'user',
      related_entity: {
        entity_type: 'staff',
        entity_id: mockStaff._id
      },
      action_url: '/school-admin/staff',
      action_label: 'View Staff',
      channels: {
        in_app: true
      }
    });

    console.log('✅ Staff creation notifications created successfully!');
    console.log(`   👋 Welcome notification: ${welcomeNotification.notification_id}`);
    console.log(`   👨‍💼 Admin notification: ${adminNotification.notification_id}`);

    console.log('\n🧪 Test 3: Querying created notifications...');
    
    const allTestNotifications = await Notification.find({
      $or: [
        { recipient_id: mockUserId },
        { recipient_id: mockStaff._id }
      ]
    }).sort({ createdAt: -1 });

    console.log(`📋 Found ${allTestNotifications.length} test notifications:`);
    allTestNotifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. [${notification.type.toUpperCase()}] ${notification.title}`);
      console.log(`     📝 ${notification.message}`);
      console.log(`     🔔 Read: ${notification.read ? '✅' : '❌'}`);
      console.log(`     🎯 Priority: ${notification.priority}`);
      console.log('');
    });

    console.log('\n🧪 Test 4: Testing notification methods...');
    
    if (allTestNotifications.length > 0) {
      const firstNotification = allTestNotifications[0];
      
      // Test marking as read
      await firstNotification.markAsRead();
      console.log(`✅ Marked "${firstNotification.title}" as read`);
      
      // Test checking delivery channel
      const shouldDeliverEmail = firstNotification.shouldDeliverVia('email');
      console.log(`📧 Should deliver via email: ${shouldDeliverEmail}`);
    }

    console.log('\n🧪 Test 5: Testing bulk notification creation...');
    
    const bulkRecipients = [
      new mongoose.Types.ObjectId(),
      new mongoose.Types.ObjectId(),
      new mongoose.Types.ObjectId()
    ];

    const bulkNotifications = await NotificationService.createBulkNotifications(bulkRecipients, {
      school_id: mockSchoolId,
      type: 'announcement',
      category: 'system',
      title: '📢 System Announcement',
      message: 'This is a bulk notification test for system announcements.',
      sender_type: 'system',
      priority: 'normal',
      channels: {
        in_app: true
      }
    });

    console.log(`✅ Created ${bulkNotifications.length} bulk notifications`);

    console.log('\n🎉 All notification tests completed successfully!');
    
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Basic notification creation: PASSED');
    console.log('   ✅ Staff creation notifications: PASSED');
    console.log('   ✅ Notification querying: PASSED');
    console.log('   ✅ Notification methods: PASSED');
    console.log('   ✅ Bulk notifications: PASSED');

    console.log('\n🚀 Next Steps:');
    console.log('   1. Start the backend server: npm start');
    console.log('   2. Test creating a staff member via the frontend');
    console.log('   3. Check notifications in the dashboard header');
    console.log('   4. Verify notifications appear in real-time');

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await Notification.deleteMany({
      $or: [
        { recipient_id: mockUserId },
        { recipient_id: mockStaff._id },
        { recipient_id: { $in: bulkRecipients } }
      ]
    });
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n👋 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  require('dotenv').config();
  simpleNotificationTest();
}

module.exports = simpleNotificationTest;
