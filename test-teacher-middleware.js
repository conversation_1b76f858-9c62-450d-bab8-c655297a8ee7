// Test script to verify teacher middleware functionality
const mongoose = require('mongoose');
const User = require('./src/models/User');
const StaffPermission = require('./src/models/StaffPermission');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/scholarify', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testTeacherMiddleware() {
  try {
    console.log('🧪 Testing Teacher Middleware Logic...\n');

    // Find a teacher user
    const teacher = await User.findOne({ role: 'teacher' });
    if (!teacher) {
      console.log('❌ No teacher found in database');
      return;
    }

    console.log('✅ Found teacher:', {
      _id: teacher._id,
      name: teacher.name,
      email: teacher.email,
      school_ids: teacher.school_ids,
      access_codes: teacher.access_codes
    });

    // Check if teacher has school access
    const schoolId = teacher.school_ids[0] || (teacher.access_codes[0] && teacher.access_codes[0].school_id);
    if (!schoolId) {
      console.log('❌ Teacher has no school access');
      return;
    }

    console.log('🏫 Testing school access for school:', schoolId);

    // Check school access logic
    const hasSchoolAccess = teacher.school_ids.includes(schoolId) || 
                           teacher.access_codes.some(code => 
                             code.school_id.toString() === schoolId.toString() && code.is_active
                           );

    console.log('✅ School access check:', hasSchoolAccess);

    // Get teacher's permissions
    const staffPermission = await StaffPermission.findOne({
      user_id: teacher._id,
      school_id: schoolId,
      is_active: true
    }).populate('assigned_classes.class_id');

    if (!staffPermission) {
      console.log('❌ No staff permissions found for this teacher');
      return;
    }

    console.log('✅ Found staff permissions:', {
      role_template: staffPermission.role_template,
      assigned_classes: staffPermission.assigned_classes.length,
      permissions: Object.keys(staffPermission.permissions)
    });

    // Test permission check
    const hasGradePermission = staffPermission.permissions.academic_records && 
                              staffPermission.permissions.academic_records.view_grades_assigned_classes;

    console.log('✅ Can view grades:', hasGradePermission);

    // Test class assignments
    const assignedClassIds = staffPermission.assigned_classes.map(cls => cls.class_id._id.toString());
    const assignedSubjects = staffPermission.assigned_classes.flatMap(cls => cls.subjects);

    console.log('✅ Assigned classes:', assignedClassIds);
    console.log('✅ Assigned subjects:', assignedSubjects);

    console.log('\n🎉 Teacher middleware test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing teacher middleware:', error);
  } finally {
    mongoose.connection.close();
  }
}

testTeacherMiddleware();
