// Test script for teacher assignment endpoints
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const ClassSchedule = require('./src/models/ClassSchedule');
const User = require('./src/models/User');
const Class = require('./src/models/Class');
const Subject = require('./src/models/Subject');

async function testTeacherAssignments() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get actual IDs from the database
    const schools = await mongoose.connection.db.collection('schools').findOne();
    const classes = await mongoose.connection.db.collection('classes').findOne();
    const subjects = await mongoose.connection.db.collection('subjects').findOne();

    if (!schools || !classes || !subjects) {
      console.log('No test data found. Please ensure you have schools, classes, and subjects in your database.');
      return;
    }

    const schoolId = schools._id;
    const classId = classes._id;
    const subjectId = subjects._id;

    console.log('\n=== Testing Teacher Assignment Queries ===\n');

    // Test 1: Get all teacher assignments for a class (NEW LOGIC)
    console.log('1. Testing getTeacherAssignmentsByClass (NEW LOGIC)...');
    const classAssignments = await ClassSchedule.find({
      school_id: schoolId,
      class_id: classId,
      teacher_id: { $exists: true, $ne: null }
    })
    .populate('teacher_id', 'first_name last_name email')
    .populate('subject_id', 'name')
    .select('teacher_id subject_id');

    console.log(`Found ${classAssignments.length} schedule entries for class`);

    // Group by subject and get the first (and should be only) teacher for each subject
    const subjectTeachers = {};

    classAssignments.forEach(entry => {
      if (entry.teacher_id && entry.subject_id) {
        const subjectId = entry.subject_id._id.toString();

        // Only add if we haven't seen this subject before (first teacher wins)
        if (!subjectTeachers[subjectId]) {
          const subjectName = entry.subject_id.name;
          const teacherName = entry.teacher_id.first_name && entry.teacher_id.last_name ?
            `${entry.teacher_id.first_name} ${entry.teacher_id.last_name}`
            : 'Unknown Teacher';

          subjectTeachers[subjectId] = {
            subject_id: subjectId,
            subject_name: subjectName,
            primary_teacher: teacherName,
            teacher_id: entry.teacher_id._id,
            teacher_email: entry.teacher_id.email
          };
        }
      }
    });

    // Convert to array format
    const assignments = Object.values(subjectTeachers);

    console.log('Formatted assignments (NEW):', JSON.stringify(assignments, null, 2));

    // Test 2: Get teacher for specific class and subject (NEW LOGIC)
    console.log('\n2. Testing getTeacherForClassSubject (NEW LOGIC)...');
    const specificAssignment = await ClassSchedule.findOne({
      school_id: schoolId,
      class_id: classId,
      subject_id: subjectId,
      teacher_id: { $exists: true, $ne: null }
    })
    .populate('teacher_id', 'first_name last_name email');

    if (!specificAssignment || !specificAssignment.teacher_id) {
      console.log('No teacher assigned to this class and subject');
    } else {
      // Format response
      const teacher = {
        teacher_id: specificAssignment.teacher_id._id,
        teacher_name: `${specificAssignment.teacher_id.first_name} ${specificAssignment.teacher_id.last_name}`,
        teacher_email: specificAssignment.teacher_id.email
      };

      console.log('Teacher for specific subject (NEW):', JSON.stringify(teacher, null, 2));
    }

    // Test 3: Check if we have any data at all
    console.log('\n3. Checking available data...');
    const totalSchedules = await ClassSchedule.countDocuments({ school_id: schoolId });
    const totalClasses = await Class.countDocuments({ school_id: schoolId });
    const totalSubjects = await Subject.countDocuments({ school_id: schoolId });
    const totalTeachers = await User.countDocuments({ 
      school_ids: schoolId, 
      role: 'teacher' 
    });

    console.log(`Total schedules: ${totalSchedules}`);
    console.log(`Total classes: ${totalClasses}`);
    console.log(`Total subjects: ${totalSubjects}`);
    console.log(`Total teachers: ${totalTeachers}`);

    // Test 4: Show sample data structure
    console.log('\n4. Sample schedule entry:');
    const sampleSchedule = await ClassSchedule.findOne({ school_id: schoolId })
      .populate('teacher_id', 'first_name last_name')
      .populate('subject_id', 'name')
      .populate('class_id', 'name');
    
    if (sampleSchedule) {
      console.log('Sample schedule:', {
        teacher: sampleSchedule.teacher_id ? 
          `${sampleSchedule.teacher_id.first_name} ${sampleSchedule.teacher_id.last_name}` : 
          'No teacher',
        subject: sampleSchedule.subject_id?.name || 'No subject',
        class: sampleSchedule.class_id?.name || 'No class',
        period: sampleSchedule.period,
        day: sampleSchedule.day
      });
    } else {
      console.log('No schedule entries found');
    }

  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the test
testTeacherAssignments();
