const mongoose = require('mongoose');
const NotificationService = require('./src/services/notificationService');
const Notification = require('./src/models/Notification');
const User = require('./src/models/User');
const School = require('./src/models/School');

// Test script to verify notification system
async function testNotificationSystem() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');

    // Test 1: Create a simple notification
    console.log('\n🧪 Test 1: Creating a simple notification...');
    
    // Find a test user (you might need to adjust this based on your data)
    const testUser = await User.findOne({ role: 'school_admin' });
    const testSchool = await School.findOne();
    
    if (!testUser || !testSchool) {
      console.log('❌ No test user or school found. Please create some test data first.');
      return;
    }
    
    console.log(`📧 Test user: ${testUser.first_name} ${testUser.last_name} (${testUser.email})`);
    console.log(`🏫 Test school: ${testSchool.name}`);

    // Create a test notification
    const testNotification = await NotificationService.createNotification({
      recipient_id: testUser._id,
      school_id: testSchool._id,
      type: 'info',
      category: 'system',
      title: '🧪 Test Notification',
      message: 'This is a test notification to verify the system is working correctly.',
      sender_type: 'system',
      action_url: '/test',
      action_label: 'View Test',
      priority: 'normal',
      channels: {
        in_app: true,
        email: false,
        sms: false
      }
    });

    console.log('✅ Test notification created:', testNotification.notification_id);

    // Test 2: Create bulk notifications
    console.log('\n🧪 Test 2: Creating bulk notifications...');
    
    const allUsers = await User.find({ school_ids: testSchool._id }).limit(3);
    const userIds = allUsers.map(user => user._id);
    
    if (userIds.length > 0) {
      const bulkNotifications = await NotificationService.createBulkNotifications(userIds, {
        school_id: testSchool._id,
        type: 'announcement',
        category: 'system',
        title: '📢 Bulk Test Notification',
        message: 'This is a bulk notification sent to multiple users for testing.',
        sender_type: 'system',
        priority: 'low',
        channels: {
          in_app: true
        }
      });
      
      console.log(`✅ Created ${bulkNotifications.length} bulk notifications`);
    }

    // Test 3: Test staff creation notification (simulate)
    console.log('\n🧪 Test 3: Testing staff creation notifications...');
    
    const mockNewStaff = {
      _id: new mongoose.Types.ObjectId(),
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      role: 'teacher'
    };

    // Simulate staff creation notifications
    try {
      // Welcome notification for new staff
      await NotificationService.createNotification({
        recipient_id: mockNewStaff._id,
        school_id: testSchool._id,
        type: 'success',
        category: 'staff',
        title: 'Welcome to the Team! 🎉',
        message: `Welcome to ${testSchool.name}! Your account has been created as a teacher. Please check your email for login instructions.`,
        sender_id: testUser._id,
        sender_type: 'user',
        action_url: '/login',
        action_label: 'Login Now',
        priority: 'high',
        channels: {
          in_app: true,
          email: true
        }
      });

      // Notification to admin about new staff
      await NotificationService.createNotification({
        recipient_id: testUser._id,
        school_id: testSchool._id,
        type: 'info',
        category: 'staff',
        title: 'New Staff Member Added',
        message: `${mockNewStaff.first_name} ${mockNewStaff.last_name} has been added as a teacher.`,
        sender_id: testUser._id,
        sender_type: 'user',
        related_entity: {
          entity_type: 'staff',
          entity_id: mockNewStaff._id
        },
        action_url: '/school-admin/staff',
        action_label: 'View Staff',
        channels: {
          in_app: true
        }
      });

      console.log('✅ Staff creation notifications created successfully');
    } catch (error) {
      console.error('❌ Error creating staff notifications:', error);
    }

    // Test 4: Query notifications
    console.log('\n🧪 Test 4: Querying notifications...');
    
    const userNotifications = await Notification.find({ 
      recipient_id: testUser._id 
    })
    .sort({ createdAt: -1 })
    .limit(5)
    .populate('sender_id', 'first_name last_name')
    .populate('school_id', 'name');

    console.log(`📋 Found ${userNotifications.length} notifications for test user:`);
    userNotifications.forEach((notification, index) => {
      console.log(`  ${index + 1}. [${notification.type.toUpperCase()}] ${notification.title}`);
      console.log(`     📝 ${notification.message}`);
      console.log(`     📅 ${notification.createdAt.toLocaleString()}`);
      console.log(`     👤 Read: ${notification.read ? '✅' : '❌'}`);
      console.log('');
    });

    // Test 5: Mark notification as read
    if (userNotifications.length > 0) {
      console.log('🧪 Test 5: Marking notification as read...');
      const firstNotification = userNotifications[0];
      await firstNotification.markAsRead();
      console.log(`✅ Marked notification "${firstNotification.title}" as read`);
    }

    // Test 6: Get notification stats
    console.log('\n🧪 Test 6: Getting notification statistics...');
    
    const stats = await Notification.aggregate([
      { $match: { recipient_id: testUser._id } },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: { $sum: { $cond: [{ $eq: ['$read', false] }, 1, 0] } },
          by_type: {
            $push: {
              type: '$type',
              read: '$read'
            }
          }
        }
      }
    ]);

    if (stats.length > 0) {
      console.log(`📊 Notification stats for ${testUser.first_name}:`);
      console.log(`   📧 Total: ${stats[0].total}`);
      console.log(`   🔔 Unread: ${stats[0].unread}`);
      
      const typeStats = {};
      stats[0].by_type.forEach(item => {
        if (!typeStats[item.type]) {
          typeStats[item.type] = { total: 0, unread: 0 };
        }
        typeStats[item.type].total++;
        if (!item.read) {
          typeStats[item.type].unread++;
        }
      });
      
      console.log('   📈 By type:');
      Object.entries(typeStats).forEach(([type, counts]) => {
        console.log(`      ${type}: ${counts.total} total, ${counts.unread} unread`);
      });
    }

    console.log('\n🎉 All notification tests completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Create a new staff member via the frontend');
    console.log('   2. Check the notifications in the dashboard header');
    console.log('   3. Verify notifications are created in the database');
    console.log('   4. Test marking notifications as read/unread');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n👋 Disconnected from MongoDB');
  }
}

// Run the test
if (require.main === module) {
  require('dotenv').config();
  testNotificationSystem();
}

module.exports = testNotificationSystem;
